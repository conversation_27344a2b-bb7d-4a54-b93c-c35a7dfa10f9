package com.saigeware.sh.saiwell;

import android.annotation.TargetApi;
import android.app.Service;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.bluetooth.le.ScanCallback;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Build;
import android.os.IBinder;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.jstyle.blesdk2301.callback.BleConnectionListener;
import com.jstyle.blesdk2301.callback.OnScanResults;
import com.jstyle.blesdk2301.model.Device;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * Bluetooth management class for the 2301sdk5.0
 */
public class BleManager {
    private static final String TAG = "BleManager";
    private static BleManager ourInstance;
    private String address;
    private BleService bleService;
    private ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "BleService disconnected");
            bleService = null;
        }

        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "BleService connected");
            bleService = ((BleService.LocalBinder) service).getService();
            if (!TextUtils.isEmpty(address)) {
                bleService.initBluetoothDevice(address, context, needReconnect1, bleConnectionListener1);
            }
        }
    };
    private Intent serviceIntent;
    private BluetoothAdapter bluetoothAdapter;
    private Context context;
    private boolean needReconnect1 = true;
    private BleConnectionListener bleConnectionListener1;
    private boolean isinScan = false;
    private String[] devicesName = null;
    private OnScanResults onScanResult = null;

    private BleManager(Context context) {
        this.context = context;
        if (serviceIntent == null) {
            serviceIntent = new Intent(context, BleService.class);
            context.bindService(serviceIntent, serviceConnection,
                    Service.BIND_AUTO_CREATE);
        }
        BluetoothManager bluetoothManager = (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
        if (bluetoothManager != null) {
            bluetoothAdapter = bluetoothManager.getAdapter();
        }
    }

    /**
     * Initialize the BLE Manager with the application context
     * 
     * @param context Application context
     * @return true if initialization was successful, false otherwise
     */
    public static boolean init(Context context) {
        try {
            Log.d(TAG, "Initializing BleManager");
            if (ourInstance == null) {
                synchronized (BleManager.class) {
                    if (ourInstance == null) {
                        ourInstance = new BleManager(context);
                    }
                }
            }
            return ourInstance != null && ourInstance.bluetoothAdapter != null;
        } catch (Exception e) {
            Log.e(TAG, "Error initializing BleManager: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * Reset initialization to allow another attempt
     */
    public static void resetInitialization() {
        ourInstance = null;
        Log.d(TAG, "BleManager initialization reset");
    }

    /**
     * Get the singleton instance of BleManager
     */
    public static BleManager getInstance() {
        return ourInstance;
    }

    /**
     * Check if Bluetooth is enabled
     */
    public boolean isBluetoothEnabled() {
        return bluetoothAdapter != null && bluetoothAdapter.isEnabled();
    }

    /**
     * Check if a device is currently connected
     */
    public boolean isConnected() {
        if (bleService == null) return false;
        return bleService.isConnected();
    }

    /**
     * Connect to a Bluetooth device
     */
    public void connectDevice(String address, boolean needReconnect, BleConnectionListener bleConnectionListener) {
        this.needReconnect1 = needReconnect;
        this.bleConnectionListener1 = bleConnectionListener;
        
        if (bluetoothAdapter == null) {
            Log.e(TAG, "====== RING DEBUG ====== Cannot connect: Bluetooth adapter is null");
            if (bleConnectionListener != null) {
                bleConnectionListener.ConnectionFailed();
            }
            return;
        }
        
        if (!bluetoothAdapter.isEnabled()) {
            Log.e(TAG, "====== RING DEBUG ====== Cannot connect: Bluetooth is not enabled");
            if (bleConnectionListener != null) {
                bleConnectionListener.BluetoothSwitchIsTurnedOff();
            }
            return;
        }
        
        if (TextUtils.isEmpty(address)) {
            Log.e(TAG, "====== RING DEBUG ====== Cannot connect: Address is empty");
            if (bleConnectionListener != null) {
                bleConnectionListener.ConnectionFailed();
            }
            return;
        }
        
        if (isConnected()) {
            Log.d(TAG, "====== RING DEBUG ====== Device is already connected");
            if (bleConnectionListener != null) {
                bleConnectionListener.ConnectionSucceeded();
            }
            return;
        }

        if (bleService == null) {
            this.address = address;
            Log.d(TAG, "====== RING DEBUG ====== Saving address for when service connects: " + address);
        } else {
            Log.d(TAG, "====== RING DEBUG ====== Initializing device with address: " + address);
            bleService.initBluetoothDevice(address, this.context, needReconnect, bleConnectionListener);
        }
    }

    /**
     * Disconnect the current device
     */
    public void disconnectDevice() {
        if (bleService == null) {
            Log.d(TAG, "====== RING DEBUG ====== Cannot disconnect: BleService is null");
            return;
        }
        Log.d(TAG, "====== RING DEBUG ====== Disconnecting device");
        bleService.disconnect();
    }

    /**
     * Write data to the connected device
     */
    public void writeValue(byte[] value) {
        if (bleService == null || !isConnected()) {
            Log.e(TAG, "====== RING DEBUG ====== Cannot write value: " + (bleService == null ? "BleService is null" : "Device not connected"));
            return;
        }
        Log.d(TAG, "====== RING DEBUG ====== Writing value: " + bytesToHex(value));
        bleService.writeValue(value);
    }


    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public void startDeviceScan(@NonNull String[] deviceNames, @NonNull OnScanResults scanResults) {
        Log.d(TAG, "====== RING DEBUG ====== Starting device scan for: " + Arrays.toString(deviceNames));
        if (bluetoothAdapter == null) {
            Log.e(TAG, "====== RING DEBUG ====== Cannot start scan: Bluetooth adapter is null");
            scanResults.Fail(0);
            return;
        }
        
        if (!bluetoothAdapter.isEnabled()) {
            Log.e(TAG, "====== RING DEBUG ====== Cannot start scan: Bluetooth is not enabled");
            scanResults.Fail(1);
            return;
        }

        // Store callback and device names
        this.devicesName = deviceNames;
        this.onScanResult = scanResults;
        isinScan = true;

        Log.d(TAG, "====== RING DEBUG ====== Starting BLE scan for devices: " + Arrays.toString(deviceNames));

        // For Android 5.0+ (API 21+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ScanSettings.Builder builder = new ScanSettings.Builder();
            builder.setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY);
            bluetoothAdapter.getBluetoothLeScanner().startScan(null, builder.build(), leScanCallback);
        } else {
            // For older versions
            bluetoothAdapter.startLeScan(legacyLeScanCallback);
        }
    }

    /**
     * Stop scanning for devices
     */
    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public void stopScanDevices() {
        Log.d(TAG, "====== RING DEBUG ====== Stopping device scan");
        isinScan = false;
        
        if (bluetoothAdapter == null) {
            Log.e(TAG, "====== RING DEBUG ====== Cannot stop scan: Bluetooth adapter is null");
            return;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            if (bluetoothAdapter.getBluetoothLeScanner() != null) {
                try {
                    bluetoothAdapter.getBluetoothLeScanner().stopScan(leScanCallback);
                    Log.d(TAG, "====== RING DEBUG ====== Successfully stopped Bluetooth LE scanner");
                } catch (Exception e) {
                    Log.e(TAG, "====== RING DEBUG ====== Error stopping Bluetooth LE scanner: " + e.getMessage(), e);
                }
            } else {
                Log.e(TAG, "====== RING DEBUG ====== Cannot stop scan: BluetoothLeScanner is null");
            }
        } else {
            try {
                bluetoothAdapter.stopLeScan(legacyLeScanCallback);
                Log.d(TAG, "====== RING DEBUG ====== Successfully stopped legacy Bluetooth LE scan");
            } catch (Exception e) {
                Log.e(TAG, "====== RING DEBUG ====== Error stopping legacy Bluetooth LE scan: " + e.getMessage(), e);
            }
        }
    }

    /**
     * Check if the device name matches any of the specified device names
     */
    private boolean matchesDeviceNames(@NonNull String name) {
        if (devicesName == null || devicesName.length == 0) {
            return false;
        }
        
        for (String deviceName : devicesName) {
            if (name.toLowerCase().contains(deviceName.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Scan callback for Android Lollipop and above
     */
    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    private final ScanCallback leScanCallback = new ScanCallback() {
        @Override
        public void onScanResult(int callbackType, ScanResult result) {
            BluetoothDevice device = result.getDevice();
            String name = device.getName();
            
            if (name != null) {
                Log.d(TAG, "====== RING DEBUG ====== Scan result: " + name + " " + device.getAddress() + " RSSI: " + result.getRssi());
                
                if (matchesDeviceNames(name)) {
                    Log.d(TAG, "====== RING DEBUG ====== Found matching device: " + name);
                    // Convert to our device model
                    try {
                        Device bleDevice = new Device();
                        bleDevice.setName(name);
                        bleDevice.setMac(device.getAddress());
                        bleDevice.setRiss(result.getRssi());
                        
                        if (onScanResult != null && isinScan) {
                            onScanResult.Success(bleDevice);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "====== RING DEBUG ====== Error handling scan result: " + e.getMessage(), e);
                    }
                } else {
                    Log.d(TAG, "====== RING DEBUG ====== Device doesn't match filter criteria: " + name);
                }
            }
        }

        @Override
        public void onBatchScanResults(List<ScanResult> results) {
            Log.d(TAG, "Batch scan results: " + results.size() + " devices found");
            for (ScanResult scanResult : results) {
                onScanResult(ScanSettings.CALLBACK_TYPE_ALL_MATCHES, scanResult);
            }
        }

        @Override
        public void onScanFailed(int errorCode) {
            Log.e(TAG, "Scan failed with error code: " + errorCode);
            if (onScanResult != null) {
                onScanResult.Fail(errorCode);
            }
        }
    };

    /**
     * Scan callback for pre-Lollipop devices
     */
    private final BluetoothAdapter.LeScanCallback legacyLeScanCallback = (device, rssi, scanRecord) -> {
        String name = device.getName();
        if (name != null) {
            Log.d(TAG, "Legacy scanned device: " + name + " (" + device.getAddress() + "), RSSI: " + rssi);
            if (matchesDeviceNames(name.toLowerCase())) {
                Log.d(TAG, "Legacy device matched filter: " + name);
                Device deviceModel = new Device();
                deviceModel.setBluetoothDevice(device);
                deviceModel.setName(name);
                deviceModel.setMac(device.getAddress());
                deviceModel.setIsdfu(name.toLowerCase().contains("dfu"));
                deviceModel.setIsconted(false);
                deviceModel.setPaired(false);
                deviceModel.setRiss(rssi);
                onScanResult.Success(deviceModel);
            }
        }
    };

    /**
     * Converts byte array to hexadecimal string
     */
    public static String bytesToHex(byte[] bytes) {
        if (bytes == null) return "";
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString().trim();
    }
} 