package com.saigeware.sh.saiwell;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import io.flutter.plugin.common.MethodChannel.Result;
import java.util.LinkedList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

/**
 * CommandQueue handles the sequential sending of BLE commands to the device.
 * This ensures that only one command is processed at a time, which prevents
 * BLE communication issues when commands are sent too quickly.
 */
public class CommandQueue {
    private static final String TAG = "CommandQueue";
    private LinkedList<CommandTask> queue = new LinkedList<>();
    private boolean isProcessing = false;
    private final BleManager bleManager;
    private final HashMap<String, Result> pendingResults;
    private final Handler timeoutHandler = new Handler(Looper.getMainLooper());
    private static final int COMMAND_TIMEOUT_MS = 15000; // 15 seconds timeout
    
    // Set of commands that typically send multipart responses
    private static final Set<String> MULTIPART_COMMANDS = new HashSet<>();
    
    // Track the last command we sent to handle multipart responses
    private String lastCommandType = null;
    private boolean commandCompleted = false;

    public CommandQueue(BleManager bleManager, HashMap<String, Result> pendingResults) {
        this.bleManager = bleManager;
        this.pendingResults = pendingResults;
        
        // Initialize known multipart command types
        MULTIPART_COMMANDS.add("25"); // activity data
        MULTIPART_COMMANDS.add("26"); // sleep data
        MULTIPART_COMMANDS.add("59"); // temperature data
        MULTIPART_COMMANDS.add("91"); // HRV data
    }

    /**
     * Class to hold command tasks in the queue
     */
    public static class CommandTask {
        final byte[] command;
        final String responseType;
        final Result result;
        
        CommandTask(byte[] command, String responseType, Result result) {
            this.command = command;
            this.responseType = responseType;
            this.result = result;
        }
    }

    /**
     * Adds a command to the queue and starts processing if not already processing
     */
    public void queueCommand(byte[] command, String responseType, Result result) {
        Log.d(TAG, "====== RING DEBUG ====== Queuing command for: " + responseType);
        
        if (bleManager == null || !bleManager.isConnected()) {
            Log.e(TAG, "====== RING DEBUG ====== Cannot queue command - not connected");
            result.error("CONNECTION_ERROR", "Device not connected", null);
            return;
        }
        
        // Add command to queue
        CommandTask task = new CommandTask(command, responseType, result);
        queue.add(task);
        
        // Start processing if not already processing
        if (!isProcessing) {
            processNextCommand();
        } else {
            Log.d(TAG, "====== RING DEBUG ====== Queue is busy, command added to waiting queue. Queue size: " + queue.size());
        }
    }

    /**
     * Process the next command in the queue
     */
    private void processNextCommand() {
        if (queue.isEmpty()) {
            isProcessing = false;
            Log.d(TAG, "====== RING DEBUG ====== Command queue is empty, processing complete");
            return;
        }
        
        isProcessing = true;
        CommandTask task = queue.peek(); // Get but don't remove
        
        if (task != null) {
            Log.d(TAG, "====== RING DEBUG ====== Processing command for: " + task.responseType);
            
            // Clear any existing pending result for this response type
            if (pendingResults.containsKey(task.responseType)) {
                Log.w(TAG, "====== RING DEBUG ====== Overwriting pending result for: " + task.responseType);
            }
            
            // Store result in the pending results map
            pendingResults.put(task.responseType, task.result);
            
            // Record this as the last command type we sent
            lastCommandType = task.responseType;
            commandCompleted = false;
            
            // Set a timeout for this command
            timeoutHandler.postDelayed(() -> {
                if (pendingResults.containsKey(task.responseType)) {
                    Log.e(TAG, "====== RING DEBUG ====== Command timed out for: " + task.responseType);
                    Result pendingResult = pendingResults.remove(task.responseType);
                    if (pendingResult != null) {
                        pendingResult.error("TIMEOUT", "Command timed out", null);
                    }
                    // Move to next command
                    queue.poll(); // Remove the timed out command
                    lastCommandType = null; // Clear the last command since we're moving on
                    processNextCommand();
                }
            }, COMMAND_TIMEOUT_MS);
            
            // Send the command
            try {
                Log.d(TAG, "====== RING DEBUG ====== Writing value to device...");
                bleManager.writeValue(task.command);
            } catch (Exception e) {
                Log.e(TAG, "====== RING DEBUG ====== Error writing to device: " + e.getMessage(), e);
                pendingResults.remove(task.responseType);
                task.result.error("COMMAND_ERROR", "Error sending command: " + e.getMessage(), null);
                
                // Cancel timeout
                timeoutHandler.removeCallbacksAndMessages(null);
                
                // Move to next command
                queue.poll(); // Remove the failed command
                lastCommandType = null; // Clear the last command since we're moving on
                processNextCommand();
            }
        }
    }

    /**
     * Complete a command and move to next one in queue
     */
    public void completeCommand(String responseType) {
        // If this is a follow-up response to a multipart command, don't advance the queue
        if (isFollowUpResponse(responseType)) {
            Log.d(TAG, "====== RING DEBUG ====== Received follow-up response for " + responseType + 
                      ", continuing to wait for dataEnd=true");
            return;
        }
        
        // Cancel any pending timeout
        timeoutHandler.removeCallbacksAndMessages(null);
        
        if (!queue.isEmpty()) {
            CommandTask currentTask = queue.peek();
            
            if (currentTask != null) {
                // Check if this is the expected response or a mismatched response
                if (currentTask.responseType.equals(responseType)) {
                    // This is the expected response
                    Log.d(TAG, "====== RING DEBUG ====== Completing command for: " + responseType);
                } else {
                    // Got a different response type than expected
                    Log.w(TAG, "====== RING DEBUG ====== Received response for: " + responseType + 
                          " but expected: " + currentTask.responseType + ". Forcing command completion.");
                }
                
                // Remove from queue regardless - this prevents the queue from getting stuck
                queue.poll();
                lastCommandType = null; // Clear last command type since we're done
                commandCompleted = true;
                
                // Process next command after a small delay to give the device time to recover
                new Handler(Looper.getMainLooper()).postDelayed(this::processNextCommand, 1000);
            }
        } else {
            Log.w(TAG, "====== RING DEBUG ====== Received completion for " + responseType + " but queue is empty");
        }
    }
    
    /**
     * Checks if this is a follow-up response for a command that's already been processed.
     * This handles multi-part responses where the device may send multiple data packets.
     */
    private boolean isFollowUpResponse(String responseType) {
        // If we haven't sent a command yet, this can't be a follow-up
        if (lastCommandType == null) {
            return false;
        }
        
        // If we already completed the command and this is the same response type,
        // it's a follow-up to a completed command (which happens with multi-part data)
        if (commandCompleted && responseType.equals(lastCommandType)) {
            return true;
        }
        
        // If it's a known multi-part command type and not the one we're expecting,
        // treat it as a follow-up to handle device firmware quirks
        if (MULTIPART_COMMANDS.contains(responseType) && !responseType.equals(lastCommandType)) {
            return true;
        }
        
        return false;
    }
}
