import 'package:SAiWELL/modules/camera/binding/camera_binding.dart';
import 'package:SAiWELL/modules/camera/pages/camera_capture_screen.dart';
import 'package:SAiWELL/modules/camera/pages/camera_instruction_screen.dart';
import 'package:SAiWELL/modules/camera/pages/camera_preview_screen.dart';
import 'package:SAiWELL/modules/no_internet/no_internet_screen.dart';

import 'package:SAiWELL/modules/ppg/screens/ppg_graph_screen.dart';
import 'package:SAiWELL/modules/ppg/screens/ppg_instruction_screen.dart';
import 'package:SAiWELL/modules/recording/binding/recording_binding.dart';
import 'package:SAiWELL/modules/recording/pages/playback_screen.dart';
import 'package:SAiWELL/modules/recording/pages/recording_instruction_screen.dart';
import 'package:SAiWELL/modules/recording/pages/recording_screen.dart';
import 'package:SAiWELL/modules/ring/binding/ring_binding.dart';
import 'package:SAiWELL/modules/ring/connecting_ring_screen.dart';
import 'package:SAiWELL/modules/ring/ring_home_screen.dart';
import 'package:SAiWELL/modules/ring/ring_list_screen.dart';
import 'package:SAiWELL/modules/ring/searching_ring_screen.dart';
import 'package:SAiWELL/modules/splash/binding/splash_binding.dart';
import 'package:SAiWELL/modules/splash/splash_screen.dart';
import 'package:SAiWELL/modules/temp/binding/temp_binding.dart';
import 'package:SAiWELL/modules/update_available/check_app_update_screen.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

import 'modules/faceMesh/binding/face_detection_binding.dart';
import 'modules/faceMesh/views/face_detection_screen.dart';
import 'modules/home/<USER>/home_binding.dart';
import 'modules/home/<USER>';
import 'modules/temp/screen/temp_measurement_screen.dart';
import 'package:SAiWELL/modules/ppg/binding/ppg_binding.dart';

class AppRoutes {
  static List<GetPage> getPages = [
    GetPage(
      name: SplashScreen.routeName,
      page: () => const SplashScreen(),
      bindings: SplashBinding.binding,
    ),
    GetPage(
      name: HomeScreen.routeName,
      page: () => const HomeScreen(),
      bindings: HomeBinding.binding,
    ),
    GetPage(
      name: RingHomeScreen.routeName,
      page: () => const RingHomeScreen(),
      bindings: RingBinding.binding,
    ),
    GetPage(
      name: SearchingRingScreen.routeName,
      page: () => const SearchingRingScreen(),
      bindings: RingBinding.binding,
    ),
    GetPage(
      name: RingListScreen.routeName,
      page: () => const RingListScreen(),
      bindings: RingBinding.binding,
    ),
    GetPage(
      name: ConnectingRingScreen.routeName,
      page: () => const ConnectingRingScreen(),
      bindings: RingBinding.binding,
    ),
    GetPage(
      name: CheckAppUpdateScreen.routeName,
      page: () => const CheckAppUpdateScreen(),
      bindings: HomeBinding.binding,
    ),
    GetPage(
      name: RecordingInstructionScreen.routeName,
      page: () => const RecordingInstructionScreen(),
      bindings: InstructionBinding.binding,
    ),
    GetPage(
      name: RecordingScreen.routeName,
      page: () => const RecordingScreen(),
      bindings: RecordingBinding.binding,
    ),
    GetPage(
      name: PlaybackScreen.routeName,
      page: () => const PlaybackScreen(),
      bindings: PlaybackBinding.binding,
    ),
    GetPage(
      name: CameraInstructionScreen.routeName,
      page: () => const CameraInstructionScreen(),
      bindings: CameraBinding.binding,
    ),
    GetPage(
      name: CameraCaptureScreen.routeName,
      page: () => const CameraCaptureScreen(),
    ),
    GetPage(
      name: CameraPreviewScreen.routeName,
      page: () => const CameraPreviewScreen(),
    ),
    GetPage(
      name: FaceDetectionScreen.routeName,
      page: () => const FaceDetectionScreen(),
      bindings: FaceDetectionBinding.binding,
    ),
    GetPage(
      name: TempMeasurementScreen.routeName,
      page: () => const TempMeasurementScreen(),
      bindings: TempBinding.binding,
    ),
    GetPage(
      name: PpgInstructionScreen.routeName,
      page: () => const PpgInstructionScreen(),
      bindings: PpgBinding.binding,
    ),
    GetPage(
      name: PpgGraphScreen.routeName,
      page: () => const PpgGraphScreen(),
      bindings: PpgBinding.binding,
    ),
    GetPage(
      name: NoInternetScreen.routeName,
      page: () => const NoInternetScreen(),
    ),
  ];
}
