import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:SAiWELL/models/health_datatype.dart';
import 'package:arkit_plugin/arkit_plugin.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:vector_math/vector_math_64.dart' as vector;
import '../../../utils/dialogs/success_recording_dialog.dart';
import '../../../utils/reusableWidgets/reusable_dialog.dart';
import '../../home/<USER>';
import 'package:SAiWELL/services/gcs_upload_service.dart';
import 'package:google_mlkit_face_mesh_detection/google_mlkit_face_mesh_detection.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:typed_data';

import '../../../models/combined_firebase_model.dart';

class FaceMeshIosController extends GetxController {
  ARKitController? arkitController;
  ARKitFaceAnchor? latestFaceAnchor;
  final RxBool isProcessing = false.obs;
  final RxBool isDataUploading = false.obs;
  Timer? _animationTimer;
  final RxBool showInstructions = true.obs;

  final RxString currentHint =
      RxString('Please position your face in the frame');
  final RxBool showManualButton = true.obs;
  Timer? _faceDetectionTimer;
  DateTime? _faceFirstDetectedTime;
  vector.Vector3? _lastFacePosition;
  static const _maxMovementThreshold = 0.008;
  static const _requiredDetectionDuration = 3;
  final RxBool canCapture = true.obs;

  Timer? _noFaceDetectionTimer;
  final RxBool showRetryPopup = false.obs;
  final RxBool isProcessingStopped = false.obs;
  static const _noFaceDetectionThreshold = 5;

  List<ARKitNode> horizontalLineNodes = [];
  List<ARKitNode> verticalLineNodes = [];

  double lineThickness = 0.0009;

  final whiteMaterial = ARKitMaterial(
    diffuse: const ARKitMaterialColor(Colors.green),
    lightingModelName: ARKitLightingModel.constant,
    transparency: .62,
  );

  @override
  void onInit() {
    super.onInit();
    canCapture.value = true;
    _startAnimation();
  }

  @override
  void onClose() {
    _resetAutoCapture();
    _noFaceDetectionTimer?.cancel();
    _animationTimer?.cancel();
    _faceDetectionTimer?.cancel();

    // Clean up ARKit nodes
    if (arkitController != null) {
      // Remove all created nodes
      for (var node in horizontalLineNodes) {
        arkitController?.remove(node.name);
      }
      for (var node in verticalLineNodes) {
        arkitController?.remove(node.name);
      }

      // Dispose the ARKitController
      arkitController?.dispose();
      arkitController = null;
    }

    // Clear node lists
    horizontalLineNodes.clear();
    verticalLineNodes.clear();

    // Reset other state
    latestFaceAnchor = null;
    _lastFacePosition = null;
    _faceFirstDetectedTime = null;

    super.onClose();
  }

  void onARKitViewCreated(ARKitController controller) {
    arkitController = controller;
    arkitController?.onAddNodeForAnchor = _handleAddAnchor;
    arkitController?.onUpdateNodeForAnchor = _handleUpdateAnchor;
  }

  void _handleAddAnchor(ARKitAnchor anchor) {
    if (arkitController == null || anchor is! ARKitFaceAnchor) return;
    latestFaceAnchor = anchor;

    // Reset no face detection timer when a face is initially detected
    _cancelNoFaceDetectionTimer();

    // Create vertical line nodes if not already created
    if (verticalLineNodes.isEmpty) {
      for (int i = 0; i < 20; i++) {
        final node = ARKitNode(
          geometry: ARKitBox(
            width: lineThickness,
            height: lineThickness,
            length: 0.01, // Initial length, will be updated later
            materials: [whiteMaterial],
          ),
          name: 'vertical_line_$i',
          isHidden: true, // Initially hidden
        );
        arkitController!.add(node, parentNodeName: anchor.nodeName);
        verticalLineNodes.add(node);
      }
    }

    // Create horizontal line nodes if not already created
    if (horizontalLineNodes.isEmpty) {
      for (int i = 0; i < 20; i++) {
        final node = ARKitNode(
          geometry: ARKitBox(
            width: lineThickness,
            height: lineThickness,
            length: 0.01,
            materials: [whiteMaterial],
          ),
          name: 'horizontal_line_$i',
          isHidden: true,
        );
        arkitController!.add(node, parentNodeName: anchor.nodeName);
        horizontalLineNodes.add(node);
      }
    }
  }

  void updateHint(String newHint) {
    if (currentHint.value != newHint) {
      currentHint.value = newHint;
    }
  }

  void _handleUpdateAnchor(ARKitAnchor anchor) {
    if (anchor is ARKitFaceAnchor) {
      latestFaceAnchor = anchor;
      if (!isProcessingStopped.value) {
        _handleFaceDetectionUpdate();
      }
    }
  }

  void _handleFaceDetectionUpdate() {
    if (!canCapture.value || isProcessingStopped.value) return;
    final faceAnchor = latestFaceAnchor;
    if (faceAnchor == null || !faceAnchor.isTracked) {
      updateHint('Please position your face in the frame');
      _resetAutoCapture();

      // Start the timer for no face detection
      _startNoFaceDetectionTimer();
      return;
    }

    // Face is detected, cancel the no face detection timer
    _cancelNoFaceDetectionTimer();

    final currentPosition = _getFacePosition(faceAnchor);
    final now = DateTime.now();

    if (_lastFacePosition != null) {
      final distance = (currentPosition - _lastFacePosition!).length;
      if (distance > _maxMovementThreshold) {
        updateHint('Please stay still');
        _resetAutoCapture();
        _lastFacePosition = currentPosition;
        return;
      }
    }

    _lastFacePosition = currentPosition;
    updateHint('Hold still...');

    _faceFirstDetectedTime ??= now;

    final detectionDuration = now.difference(_faceFirstDetectedTime!).inSeconds;
    if (detectionDuration >= _requiredDetectionDuration) {
      _triggerAutoCapture();
    }
  }

  // Method to start the no face detection timer
  void _startNoFaceDetectionTimer() {
    // Don't start the timer if processing is already stopped
    if (isProcessingStopped.value) return;

    _noFaceDetectionTimer?.cancel();
    _noFaceDetectionTimer =
        Timer(const Duration(seconds: _noFaceDetectionThreshold), () {
      // Only show the popup if we're still in a state where no face is detected
      if ((latestFaceAnchor == null || !latestFaceAnchor!.isTracked) &&
          !showRetryPopup.value &&
          !isProcessing.value &&
          !showInstructions.value &&
          !isProcessingStopped.value) {
        _showRetryPopup();
      }
    });
  }

  // Method to cancel the no face detection timer
  void _cancelNoFaceDetectionTimer() {
    _noFaceDetectionTimer?.cancel();
    _noFaceDetectionTimer = null;
  }

  // Method to show the retry popup
  void _showRetryPopup() {
    showRetryPopup.value = true;
    isProcessingStopped.value = true;

    // Stop the animation and face tracking
    _animationTimer?.cancel();
    _animationTimer = null;

    ReusableDialog.show(
      isDismissible: false,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'No Face Detected',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          const Text(
            'Please ensure your face is properly positioned within the frame.',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _retryFaceDetection,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  // Method to handle retry
  void _retryFaceDetection() {
    showRetryPopup.value = false;
    ReusableDialog.close();

    // Clean up existing resources
    _resetAutoCapture();
    _cancelNoFaceDetectionTimer();

    // Remove existing ARKit nodes to prevent memory leaks
    if (arkitController != null) {
      // Clean up existing nodes
      for (var node in horizontalLineNodes) {
        arkitController?.remove(node.name);
      }
      for (var node in verticalLineNodes) {
        arkitController?.remove(node.name);
      }
    }

    // Clear and reset node lists
    horizontalLineNodes.clear();
    verticalLineNodes.clear();

    canCapture.value = true; // Make sure capture is enabled
    isProcessingStopped.value = false; // Resume processing
    updateHint('Please position your face in the frame');

    // Restart the animation
    _startAnimation();

    // Start the timer again to check for face detection
    _startNoFaceDetectionTimer();
  }

  vector.Vector3 _getFacePosition(ARKitFaceAnchor anchor) {
    final transform = anchor.transform;
    return vector.Vector3(
      transform.getColumn(3).x,
      transform.getColumn(3).y,
      transform.getColumn(3).z,
    );
  }

  void _resetAutoCapture() {
    _faceFirstDetectedTime = null;
    _faceDetectionTimer?.cancel();
  }

  void _triggerAutoCapture() async {
    if (!canCapture.value) return;
    _resetAutoCapture();
    showManualButton.value = false;
    updateHint('Capturing...');

    final success = await captureFaceData(Get.context!);
    if (success) {
      updateHint('Capture successful!');
      canCapture.value = false; // Disable further auto-captures
    } else {
      showManualButton.value = true;
      updateHint('Capture failed, please try again');
    }
  }

  void _startAnimation() {
    _animationTimer?.cancel();
    const frameDuration = Duration(milliseconds: 16); // ~60 FPS
    _animationTimer = Timer.periodic(frameDuration, (timer) {
      _updateLinePositions();
    });
  }

  void _updateLinePositions() {
    if (latestFaceAnchor == null ||
        arkitController == null ||
        isProcessingStopped.value) {
      return;
    }

    final vertices = latestFaceAnchor!.geometryVertices;
    if (vertices.isEmpty) return;

    // Make sure the line nodes are initialized
    if (verticalLineNodes.isEmpty || horizontalLineNodes.isEmpty) {
      _handleAddAnchor(latestFaceAnchor!);
      return;
    }

    // Calculate face bounding box
    double minX = vertices.map((v) => v.x).reduce(min);
    double maxX = vertices.map((v) => v.x).reduce(max);
    double minY = vertices.map((v) => v.y).reduce(min);
    double maxY = vertices.map((v) => v.y).reduce(max);

    // Animation progress (cycles every 2 seconds)
    final now = DateTime.now().millisecondsSinceEpoch / 1000.0;
    const cycleDuration = 2.0;
    final progress = (now % cycleDuration) / cycleDuration;

    // Current positions for the moving lines
    double currentX = minX + (maxX - minX) * progress;
    double currentY = maxY - (maxY - minY) * progress;
    const double delta = 0.005;

    // Select vertices for vertical line, limit to 21 vertices (20 segments)
    List<vector.Vector3> verticalLineVertices =
        vertices.where((v) => (v.x - currentX).abs() < delta).toList();
    verticalLineVertices.sort((a, b) => a.y.compareTo(b.y));
    List<vector.Vector3> selectedVerticalVertices =
        _selectVertices(verticalLineVertices, 21);

    // Update vertical line nodes
    for (int i = 0; i < 20; i++) {
      // Check if index is valid for verticalLineNodes
      if (i >= verticalLineNodes.length) continue;

      if (i < selectedVerticalVertices.length - 1) {
        final start = selectedVerticalVertices[i];
        final end = selectedVerticalVertices[i + 1];
        final vec = end - start;
        final length = vec.length;
        if (length > 0) {
          final midpoint = (start + end) / 2;
          final direction = vec / length;
          final quaternion = vector.Quaternion.fromTwoVectors(
              vector.Vector3(0, 0, 1), direction);
          final boxGeometry = ARKitBox(
            width: lineThickness,
            height: lineThickness,
            length: length,
            materials: [whiteMaterial],
          );

          // Remove the existing node
          arkitController!.remove(verticalLineNodes[i].name);

          // Create a new node with updated geometry
          final newNode = ARKitNode(
            geometry: boxGeometry,
            position: midpoint,
            name: 'vertical_line_$i',
          );

          // Set the transform using position, quaternion, and scale
          newNode.transform = Matrix4.compose(
            midpoint,
            quaternion,
            vector.Vector3.all(1.0), // Scale of 1 (no scaling)
          );

          // Add the new node to the scene
          arkitController!
              .add(newNode, parentNodeName: latestFaceAnchor!.nodeName);
          verticalLineNodes[i] = newNode; // Update the reference
        } else {
          verticalLineNodes[i].isHidden.value = true;
        }
      } else {
        verticalLineNodes[i].isHidden.value = true;
      }
    }

    // Select vertices for horizontal line, limit to 21 vertices (20 segments)
    List<vector.Vector3> horizontalLineVertices =
        vertices.where((v) => (v.y - currentY).abs() < delta).toList();
    horizontalLineVertices.sort((a, b) => a.x.compareTo(b.x));
    List<vector.Vector3> selectedHorizontalVertices =
        _selectVertices(horizontalLineVertices, 21);

    // Update horizontal line nodes
    for (int i = 0; i < 20; i++) {
      // Check if index is valid for horizontalLineNodes
      if (i >= horizontalLineNodes.length) continue;

      if (i < selectedHorizontalVertices.length - 1) {
        final start = selectedHorizontalVertices[i];
        final end = selectedHorizontalVertices[i + 1];
        final vec = end - start;
        final length = vec.length;
        if (length > 0) {
          final midpoint = (start + end) / 2;
          final direction = vec / length;
          final quaternion = vector.Quaternion.fromTwoVectors(
              vector.Vector3(0, 0, 1), direction);
          final boxGeometry = ARKitBox(
            width: lineThickness,
            height: lineThickness,
            length: length,
            materials: [whiteMaterial],
          );

          // Remove the existing node
          arkitController!.remove(horizontalLineNodes[i].name);

          // Create a new node with updated geometry
          final newNode = ARKitNode(
            geometry: boxGeometry,
            position: midpoint,
            name: 'horizontal_line_$i',
          );

          // Set the transform using position, quaternion, and scale
          newNode.transform = Matrix4.compose(
            midpoint,
            quaternion,
            vector.Vector3.all(1.0), // Scale of 1 (no scaling)
          );

          // Add the new node to the scene
          arkitController!
              .add(newNode, parentNodeName: latestFaceAnchor!.nodeName);
          horizontalLineNodes[i] = newNode; // Update the reference
        } else {
          horizontalLineNodes[i].isHidden.value = true;
        }
      } else {
        horizontalLineNodes[i].isHidden.value = true;
      }
    }
  }

  // Helper function to select a limited number of vertices evenly
  List<vector.Vector3> _selectVertices(
      List<vector.Vector3> vertices, int maxCount) {
    if (vertices.length <= maxCount) return vertices;
    List<vector.Vector3> selected = [];
    for (int i = 0; i < maxCount; i++) {
      int index = (i * (vertices.length - 1) / (maxCount - 1)).round();
      selected.add(vertices[index]);
    }
    return selected;
  }

  Future<bool> isARKitSupported() async {
    if (!Platform.isIOS) return false;
    final deviceInfo = DeviceInfoPlugin();
    final iosInfo = await deviceInfo.iosInfo;
    final String systemVersion = iosInfo.systemVersion;
    final int majorVersion = int.tryParse(systemVersion.split('.').first) ?? 0;
    if (majorVersion < 12) return false;
    final String deviceModel = iosInfo.modelName.toLowerCase().trim();
    final Set<String> unsupportedDevices = {
      'iphone 5',
      'iphone 5c',
      'iphone 5s',
      'iphone 6',
      'iphone 6 plus',
      'ipad air',
      'ipad air 2',
      'ipad mini',
      'ipad mini 2',
      'ipad mini 3',
      'ipad mini 4',
      'ipad (1st generation)',
      'ipad (2nd generation)',
      'ipad (3rd generation)',
      'ipad (4th generation)',
    };
    return !unsupportedDevices.contains(deviceModel);
  }

  Future<bool> captureFaceData(BuildContext context) async {
    if (latestFaceAnchor == null || isProcessing.value) return false;
    isProcessing.value = true;
    try {
      // Reset auto-capture state
      _resetAutoCapture();
      showManualButton.value = true;

      final faceData = _extractFacePoints();
      final success = await _uploadFaceData(faceData, context);
      return success;
    } finally {
      isProcessing.value = false;
    }
  }

  Map<String, double> _extractEulerAngles(Matrix4 matrix) {
    final r11 = matrix[0]; // First column, first row
    final r21 = matrix[4]; // Second column, first row
    final r31 = matrix[8]; // Third column, first row
    final r32 = matrix[9]; // Third column, second row
    final r33 = matrix[10]; // Third column, third row

    // Calculate yaw (rotation around Y axis)
    final yaw = atan2(r31, sqrt(r11 * r11 + r21 * r21)) * 180 / pi;

    // Calculate pitch (rotation around X axis)
    final pitch = atan2(-r32, r33) * 180 / pi;

    // Calculate roll (rotation around Z axis)
    final roll = atan2(r21, r11) * 180 / pi;

    return {
      'yaw': yaw,
      'pitch': pitch,
      'roll': roll,
    };
  }

  String? _extractFacePoints() {
    if (latestFaceAnchor == null) return null;

    // Extract Euler angles from the face transform
    final eulerAngles = _extractEulerAngles(latestFaceAnchor!.transform);

    final vertices = latestFaceAnchor!.geometryVertices
        .map((v) => [v.x, v.y, v.z])
        .expand((e) => e)
        .toList();

    final jsonMap = <String, dynamic>{
      'nodeName': latestFaceAnchor!.nodeName,
      'identifier': latestFaceAnchor!.identifier,
      'isTracked': latestFaceAnchor!.isTracked,
      'transform': _matrixToList(latestFaceAnchor!.transform),
      'geometryVertices': vertices,
      'blendShapes': latestFaceAnchor!.blendShapes,
      'leftEyeTransform': _matrixToList(latestFaceAnchor!.leftEyeTransform),
      'rightEyeTransform': _matrixToList(latestFaceAnchor!.rightEyeTransform),
      'eulerAngles': eulerAngles,
    };

    return jsonEncode(jsonMap);
  }

  List<double> _matrixToList(Matrix4 matrix) {
    return [
      matrix[0],
      matrix[1],
      matrix[2],
      matrix[3],
      matrix[4],
      matrix[5],
      matrix[6],
      matrix[7],
      matrix[8],
      matrix[9],
      matrix[10],
      matrix[11],
      matrix[12],
      matrix[13],
      matrix[14],
      matrix[15]
    ];
  }

  String _convertGeometryVerticesToCsv(
      Map<String, dynamic> parsedData, String timestamp) {
    final List<String> headers = ['timestamp'];
    final List<dynamic> vertices =
        parsedData['geometryVertices'] as List<dynamic>;
    final int vertexCount = vertices.length ~/ 3;
    for (int i = 0; i < vertexCount; i++) {
      headers.addAll(['vertex_${i}_x', 'vertex_${i}_y', 'vertex_${i}_z']);
    }
    final List<String> values = [timestamp];
    values.addAll(vertices.map((v) => v.toString()));
    return '${headers.join(',')}\n${values.join(',')}\n';
  }

  String _convertFaceDataToCsv(
      Map<String, dynamic> parsedData, String timestamp) {
    final List<String> headers = [];
    final List<String> values = [];

    headers.addAll(['timestamp', 'nodeName', 'identifier', 'isTracked']);
    values.addAll([
      timestamp,
      parsedData['nodeName'].toString(),
      parsedData['identifier'].toString(),
      parsedData['isTracked'].toString()
    ]);

    // Add Euler angles
    if (parsedData.containsKey('eulerAngles')) {
      final eulerAngles = parsedData['eulerAngles'] as Map<String, dynamic>;
      headers.addAll(['yaw', 'pitch', 'roll']);
      values.addAll([
        eulerAngles['yaw'].toString(),
        eulerAngles['pitch'].toString(),
        eulerAngles['roll'].toString()
      ]);
    }

    final transform = parsedData['transform'] as List<dynamic>;
    headers.addAll(List.generate(16, (i) => 'transform_$i'));
    values.addAll(transform.map((e) => e.toString()));

    final blendShapes = parsedData['blendShapes'] as Map<String, dynamic>;
    final blendShapeKeys = blendShapes.keys.toList()..sort();
    for (var key in blendShapeKeys) {
      headers.add('blendShape_$key');
      values.add(blendShapes[key].toString());
    }

    final leftEyeTransform = parsedData['leftEyeTransform'] as List<dynamic>;
    headers.addAll(List.generate(16, (i) => 'leftEyeTransform_$i'));
    values.addAll(leftEyeTransform.map((e) => e.toString()));

    final rightEyeTransform = parsedData['rightEyeTransform'] as List<dynamic>;
    headers.addAll(List.generate(16, (i) => 'rightEyeTransform_$i'));
    values.addAll(rightEyeTransform.map((e) => e.toString()));

    return '${headers.join(',')}\n${values.join(',')}\n';
  }

  Future<String> getFormattedFileName({required String suffix}) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final platform = Theme.of(Get.context!).platform == TargetPlatform.iOS
        ? 'ios'
        : 'android';
    final packageInfo = await PackageInfo.fromPlatform();
    final appVersion = 'v${packageInfo.version}';
    return '${timestamp}_${platform}_${appVersion}_$suffix';
  }

  Future<bool> _uploadFaceData(String? data, BuildContext context) async {
    if (data == null) return false;

    try {
      final parsedData = jsonDecode(data) as Map<String, dynamic>;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final timestampStr =
          DateFormat('yyyy-MM-dd HH:mm:ss.SSS').format(DateTime.now());

      // Generate CSVs
      final csvGeometry =
          _convertGeometryVerticesToCsv(parsedData, timestampStr);
      final csvFaceData = _convertFaceDataToCsv(parsedData, timestampStr);

      // Get user ID
      String uid = await prefsService.getUid();

      // Define file names
      String baseName = 'face_scan_$timestamp';
      String geometryFileName = '${baseName}_geometry.csv';
      String faceDataFileName = '${baseName}_facedata.csv';

      // Upload both files
      bool geometrySuccess = await uploadFaceMeshDataToGCS(
        csvData: csvGeometry,
        uid: uid,
        type: 'geometry',
        timestamp: timestamp,
        context: context,
      );

      bool faceDataSuccess = await uploadFaceMeshDataToGCS(
        csvData: csvFaceData,
        uid: uid,
        type: 'facedata',
        timestamp: timestamp,
        context: context,
      );

      if (geometrySuccess && faceDataSuccess) {
        print(
            "geometrySuccess : $uid/$geometryFileName\nfaceDataSuccess : $uid/$faceDataFileName");

        // Clean up resources before navigating away
        _animationTimer?.cancel();

        successfullyUploadedDialog(
          context: context,
          message: "Your Face data has been uploaded successfully!",
          onTap: () async {
            ReusableDialog.close();
            // Clean up all resources when leaving the screen
            _cleanUpResources();
            Get.until((route) => route.settings.name == HomeScreen.routeName);
          },
        );
        return true;
      } else {
        print("One or more uploads failed");
        return false;
      }
    } catch (e) {
      print('Upload error: $e');
      return false;
    }
  }

  Future<bool> uploadFaceMeshDataToGCS({
    required String csvData,
    required String uid,
    required String type,
    required int timestamp,
    required BuildContext context,
  }) async {
    final success = await GcsUploadService.uploadToGcs(
      bytes: utf8.encode(csvData),
      uid: uid,
      type: type,
      timestamp: timestamp,
      contentType: 'text/csv',
      extension: 'csv',
      credentialsAssetPath: 'assets/gCloud/credentials.json',
    );
    if (!success) {
      print("Upload failed for $type");
      return false;
    }
    return true;
  }

  void onContinuePressed() {
    showInstructions.value = false;
    isProcessingStopped.value = false; // Make sure processing is enabled
    // Start the no face detection timer when instructions are dismissed
    _startNoFaceDetectionTimer();
  }

  Future<ServiceAccountCredentials> loadServiceAccountCredentials() async {
    String jsonContent =
        await rootBundle.loadString('assets/gCloud/credentials.json');
    final Map<String, dynamic> jsonMap = jsonDecode(jsonContent);
    return ServiceAccountCredentials.fromJson(jsonMap);
  }

  // Helper method to clean up all resources
  void _cleanUpResources() {
    _resetAutoCapture();
    _noFaceDetectionTimer?.cancel();
    _animationTimer?.cancel();
    _faceDetectionTimer?.cancel();

    // Clean up ARKit nodes if they exist
    if (arkitController != null) {
      for (var node in horizontalLineNodes) {
        arkitController?.remove(node.name);
      }
      for (var node in verticalLineNodes) {
        arkitController?.remove(node.name);
      }
    }

    horizontalLineNodes.clear();
    verticalLineNodes.clear();
  }
}
