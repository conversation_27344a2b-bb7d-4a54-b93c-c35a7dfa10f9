import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:SAiWELL/common_controllers/global_controller.dart';
import 'package:SAiWELL/constants/constant.dart';
import 'package:SAiWELL/models/hrv_reading_model_v2.dart';
import 'package:SAiWELL/models/sleep_firebase_model.dart';
import 'package:SAiWELL/models/sleep_reading_model_v2.dart';
import 'package:SAiWELL/models/steps_reading_model_v2.dart';
import 'package:SAiWELL/modules/home/<USER>';
import 'package:SAiWELL/modules/recording/pages/recording_instruction_screen.dart';
import 'package:SAiWELL/modules/ring/ring_home_screen.dart';
import 'package:SAiWELL/modules/temp/screen/temp_measurement_screen.dart';
import 'package:SAiWELL/services/analytics/events.dart';
import 'package:SAiWELL/services/api_service.dart';
import 'package:SAiWELL/services/auth_service.dart';
import 'package:SAiWELL/services/firestore_service.dart';
import 'package:SAiWELL/services/health_service.dart';
import 'package:SAiWELL/services/location_service.dart';
import 'package:SAiWELL/services/prefs_service.dart';
import 'package:SAiWELL/services/ring_connector_scheduler.dart';
import 'package:SAiWELL/services/ring_data_collector.dart';
import 'package:SAiWELL/utils/const/app_const.dart';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:location/location.dart';
import 'package:synchronized/synchronized.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';

import '../../../models/hrv_firebase_v2_model.dart';
import '../../../models/navigation_model.dart';
import '../../../models/oxygen_firebase_v2_model.dart';
import '../../../models/step_firebase_v2_model.dart';
import '../../../models/temp_firebase_model.dart';
import '../../../services/native_communicator.dart';
import '../../../utils/const/app_images.dart';
import '../../../utils/reusableWidgets/reusable_snackbar.dart';
import 'package:SAiWELL/modules/camera/instruction_config.dart';
import 'package:SAiWELL/modules/camera/pages/camera_instruction_screen.dart';
import 'package:SAiWELL/modules/ppg/screens/ppg_instruction_screen.dart';
import 'package:SAiWELL/modules/ppg/screens/ppg_graph_screen.dart';
import 'package:SAiWELL/modules/ppg/controller/ppg_controller.dart';
import 'package:SAiWELL/utils/permission_handler_util.dart';
import 'package:SAiWELL/services/deep_link_service.dart';
import 'package:SAiWELL/utils/reusableWidgets/nfc_administration_dialog.dart';
import 'package:SAiWELL/services/notifications/platform/android_notification_helper.dart';
import '../../../utils/stream_utils.dart';
import 'package:SAiWELL/services/user_session_service.dart';
import 'package:SAiWELL/services/notifications/core/notification_manager.dart';

class HomeController extends GetxController with WidgetsBindingObserver {
  // Controllers & Services
  InAppWebViewController? inAppWebViewController;
  late PullToRefreshController pullToRefreshController;
  final RingDataCollector ringDataCollector = RingDataCollector();
  final PrefsService prefsService = PrefsService();
  final GlobalController globalController = Get.find<GlobalController>();
  final ppgController = Get.find<PPGController>();
  final RingConnectorScheduler ringConnectorScheduler =
      RingConnectorScheduler();
  NativeCommunicator communicator = NativeCommunicator();
  HealthService healthService = HealthService();
  AuthService authService = AuthService();

  AppLifecycleState? _lastChangeState;

  // Observable Variables
  final RxBool isPageLoading = true.obs;
  final RxBool dataCollectionStarted = false.obs;
  final RxBool isHomePageRequiredInWebView = true.obs;
  RxBool shouldShouldExtraMenu = false.obs;
  final RxString controllerWebViewUrl = webViewUrl.obs;
  final RxString currentUrl = ''.obs;
  RxInt selectedNavBarIndex = 0.obs;

  // Flag to track if user was on PPG screen
  bool wasOnPPG = false;

  // Private Variables
  final _lock = Lock();
  bool _isExecuted = false;
  StreamSubscription<LocationData>? locationSubscription;
  bool _isJavaScriptInjected = false;
  bool shouldFetchDataFromFB = true;
  Timer? refreshDebounceTimer;

  SleepFirebaseModel? sleepFirebaseModel;
  OxygenFirebaseV2Model? oxygenFirebaseV2Model;
  StepFirebaseV2Model? stepFirebaseV2Model;
  HrvFirebaseV2Model? hrvFirebaseV2Model;
  // TempFirebaseModel? tempFirebaseModel; //need to remove
  // CombinedFirebaseModel? combinedFirebaseModel;
  // SportFirebaseModel? sportFirebaseModel;

  SleepReadingModelV2? sleepReadingModelV2;
  StepsReadingModelV2? stepsReadingModelV2;
  HRVReadingModelV2? hrvReadingModelV2;

  int? lastSyncedMillisecondsSinceEpoch;

  Timer? _urlCheckTimer;
  String? _lastKnownUrl;

  var syncStatusMessage = "Syncing...".obs;

  // Variable to track if we've shown an NFC dialog
  bool hasProcessedNfcLinkOnResume = false;
  StreamSubscription<bool>? _nfcLinkSubscription;
  RxBool shouldShowNfcDialog = false.obs;
  bool _isNfcDialogCurrentlyShowing = false;
  bool _isWebViewReady = false;
  bool _hasPendingNfcDialog = false;

  List<String>? programsFromWebView;

  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    setNavbarVisibilityAndConnect();
    _initPullToRefresh();
  }

  @override
  void onReady() {
    _setupNfcLinkListener();
    // Check for initial NFC state when app starts (e.g., killed app opened by NFC)
    _checkInitialNfcState();
    super.onReady();
  }

  /// Check and show notification permissions dialog after login
  void _checkAndShowNotificationPermissionsAfterLogin() {
    // Delay to ensure the login process is complete and UI is stable
    Future.delayed(const Duration(milliseconds: 2000), () async {
      try {
        await AndroidNotificationHelper.showPostLoginNotificationDialog();
      } catch (e) {
        debugPrint(
            'Error showing notification permission dialog after login: $e');
      }
    });
  }

  @override
  void onClose() {
    _urlCheckTimer?.cancel();
    _urlCheckTimer = null;
    refreshDebounceTimer?.cancel();
    _nfcLinkSubscription?.cancel();

    // Reset NFC dialog state
    _isNfcDialogCurrentlyShowing = false;
    shouldShowNfcDialog.value = false;
    _isWebViewReady = false;
    _hasPendingNfcDialog = false;

    // Clear controller reference before disposing
    inAppWebViewController = null;

    // Then dispose the controller and other resources
    pullToRefreshController.dispose();
    locationSubscription?.cancel();

    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  void _setupNfcLinkListener() {
    try {
      final DeepLinkService deepLinkService = Get.find<DeepLinkService>();
      _nfcLinkSubscription = deepLinkService.nfcLinkStream.listenSafely((_) {
        // Only show dialog if the controller is still active
        if (Get.isRegistered<HomeController>()) {
          _showNfcDialog();
        }
      });
    } catch (e) {
      debugPrint("Error setting up NFC link listener in HomeController: $e");
    }
  }

  void _checkInitialNfcState() {
    try {
      final DeepLinkService deepLinkService = Get.find<DeepLinkService>();
      debugPrint(
          "🏷️ [NFC] Checking initial NFC state: ${deepLinkService.isNfcVialAdministration}, WebView ready: $_isWebViewReady");

      if (deepLinkService.isNfcVialAdministration) {
        debugPrint(
            "🏷️ [NFC] Initial NFC state detected, attempting to show dialog");
        // Add a delay to ensure the UI is ready and deep link processing is complete
        Future.delayed(const Duration(milliseconds: 800), () {
          _showNfcDialog();
        });
      } else {
        debugPrint("🏷️ [NFC] No initial NFC state, setting up delayed check");
        // If no initial NFC state, check again after a delay in case deep link processing is still ongoing
        Future.delayed(const Duration(milliseconds: 1000), () {
          try {
            final DeepLinkService deepLinkService = Get.find<DeepLinkService>();
            debugPrint(
                "🏷️ [NFC] Delayed check - NFC state: ${deepLinkService.isNfcVialAdministration}, dialog showing: $_isNfcDialogCurrentlyShowing, WebView ready: $_isWebViewReady");
            if (deepLinkService.isNfcVialAdministration &&
                !_isNfcDialogCurrentlyShowing) {
              debugPrint(
                  "🏷️ [NFC] Delayed NFC state detected, attempting to show dialog");
              _showNfcDialog();
            }
          } catch (e) {
            debugPrint("Error in delayed NFC check: $e");
          }
        });
      }
    } catch (e) {
      debugPrint("Error checking initial NFC state: $e");
    }
  }

  void _showNfcDialog() async {
    // Prevent multiple dialogs from being shown
    if (_isNfcDialogCurrentlyShowing || Get.isDialogOpen!) {
      debugPrint("🏷️ [NFC] Dialog already showing, skipping duplicate");
      return;
    }

    // Check if webview is ready, if not, set pending flag
    if (!_isWebViewReady) {
      debugPrint("🏷️ [NFC] WebView not ready yet, setting pending flag");
      _hasPendingNfcDialog = true;
      return;
    }

    // Check if user is logged in before showing dialog
    prefsService.getUid().then((uid) async {
      if (uid.isEmpty) {
        debugPrint("🏷️ [NFC] User not logged in, not showing NFC dialog");
        // Reset NFC flag so dialog can be shown after login if needed
        try {
          final DeepLinkService deepLinkService = Get.find<DeepLinkService>();
          deepLinkService.resetNfcFlag();
        } catch (e) {
          debugPrint("Error resetting NFC flag when user not logged in: $e");
        }
        return;
      }

      // Only proceed if programsFromWebView contains 'SFOT-SLIT'
      if (programsFromWebView != null &&
          programsFromWebView!.contains('SFOT-SLIT')) {
        _isNfcDialogCurrentlyShowing = true;

        // Delay slightly to ensure screen is fully built
        Future.delayed(const Duration(milliseconds: 300), () async {
          if (!Get.isDialogOpen! && _isNfcDialogCurrentlyShowing) {
            shouldShowNfcDialog.value = true;

            // Write to Firebase as soon as the dialog is shown
            try {
              final DeepLinkService deepLinkService =
                  Get.find<DeepLinkService>();
              final String? lastUri = deepLinkService.lastProcessedUri;
              final String? vialId = lastUri != null
                  ? lastUri.split('/').last.toLowerCase()
                  : null;
              if (vialId != null) {
                await deepLinkService.writeVialDataToFirebase(vialId);
              } else {
                debugPrint("No vialId available to write to Firebase.");
              }
            } catch (e) {
              debugPrint("Error writing NFC data to Firebase: $e");
            }

            showDialog(
              context: Get.context!,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return NfcAdministrationDialog(
                  onDismiss: () async {
                    // Store NFC scan timestamp when dialog is dismissed
                    try {
                      await prefsService.setLastNfcScanTimestamp(
                          DateTime.now().millisecondsSinceEpoch);
                      debugPrint("🏷️ [NFC] NFC scan timestamp stored");
                    } catch (e) {
                      debugPrint("Error storing NFC scan timestamp: $e");
                    }

                    // Reset the flags after dismissing
                    try {
                      final DeepLinkService deepLinkService =
                          Get.find<DeepLinkService>();
                      deepLinkService.resetNfcFlag();
                      shouldShowNfcDialog.value = false;
                      _isNfcDialogCurrentlyShowing = false;
                      _hasPendingNfcDialog = false;
                      debugPrint("🏷️ [NFC] Dialog dismissed and flags reset");
                    } catch (e) {
                      debugPrint("Error resetting NFC flag: $e");
                      _isNfcDialogCurrentlyShowing = false;
                      _hasPendingNfcDialog = false;
                    }
                  },
                );
              },
            );
          } else {
            _isNfcDialogCurrentlyShowing = false;
          }
        });
      } else {
        debugPrint(
            "🏷️ [NFC] User does not have SFOT-SLIT in programsFromWebView, skipping dialog and Firebase write.");
        // Optionally, reset NFC flag here if you want to clear it for non-eligible users
        try {
          final DeepLinkService deepLinkService = Get.find<DeepLinkService>();
          deepLinkService.resetNfcFlag();
        } catch (e) {
          debugPrint("Error resetting NFC flag for non-eligible user: $e");
        }
        return;
      }
    });
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (_lastChangeState == state || state == AppLifecycleState.inactive) {
      return;
    }
    _lastChangeState = state;
    switch (state) {
      case AppLifecycleState.resumed:
        if (kDebugMode) print("AppLifecycleState: resumed");
        String uid = await prefsService.getUid();
        if (uid.isNotEmpty) {
          // Always check health permissions, but only store successful grants
          // This way, if permissions aren't granted, dialog will continue to show
          bool hasHealthPermissions = await healthService.checkPermission();
          if (hasHealthPermissions) {
            // Only mark as checked if permissions were successfully granted
            await prefsService.setHealthPermissionChecked(true);

            // Upload health data to Firebase when app resumes
            try {
              if (kDebugMode) print("AppLifecycleState: uploading health data on app resume");
              await healthService.fetchHealthDataWithDevicesAndSources(uid);
              if (kDebugMode) print("AppLifecycleState: health data upload completed");
            } catch (e) {
              if (kDebugMode) print("AppLifecycleState: error uploading health data: $e");
            }

            // Request background authorization when returning from Health Connect
            if (Platform.isAndroid) {
              Future.delayed(const Duration(seconds: 2), () async {
                await healthService.requestBackgroundAuthentication();
              });
            }
          }
        }

        // Check for notification permission retry when app resumes
        try {
          await AndroidNotificationHelper.onAppResumed();
        } catch (e) {
          if (kDebugMode)
            print("Error checking notification permissions on resume: $e");
        }

        // Check if there's a pending NFC link when app resumes
        try {
          final DeepLinkService deepLinkService = Get.find<DeepLinkService>();
          if (deepLinkService.isNfcVialAdministration &&
              !hasProcessedNfcLinkOnResume &&
              !_isNfcDialogCurrentlyShowing) {
            hasProcessedNfcLinkOnResume = true;
            debugPrint(
                "🏷️ [NFC] App resumed with pending NFC, navigating to home and showing dialog");

            // Navigate to home screen if not already there
            if (Get.currentRoute != HomeScreen.routeName) {
              Get.offAllNamed(HomeScreen.routeName);
            }

            // Show the NFC dialog (will check if webview is ready)
            _showNfcDialog();

            // Reset flag after a delay to allow for future NFC scans
            Future.delayed(const Duration(seconds: 3), () {
              hasProcessedNfcLinkOnResume = false;
            });
          }
        } catch (e) {
          debugPrint("Error checking NFC links on resume: $e");
        }
        break;
      case AppLifecycleState.inactive:
        if (kDebugMode) print("AppLifecycleState: inactive");
        // Add logic for when the app is inactive (e.g., an incoming call or notification)
        break;
      case AppLifecycleState.paused:
        if (kDebugMode) print("AppLifecycleState: paused");
        // Add logic for when the app is paused (backgrounded but not terminated)
        break;
      case AppLifecycleState.detached:
        if (kDebugMode) print("AppLifecycleState: detached");
        // Add logic for when the app is detached (still in memory but not active)
        break;
      case AppLifecycleState.hidden:
        if (kDebugMode) print("AppLifecycleState: hidden");
        break;
    }
  }

  // Initialization Methods
  void _initPullToRefresh() {
    pullToRefreshController = PullToRefreshController(
      settings: PullToRefreshSettings(color: Colors.blue),
      onRefresh: () async {
        if (inAppWebViewController != null) {
          debugPrint("Pull to refresh triggered");

          // Check if we are on the devices page
          bool isOnRingPage = currentUrl.value.contains('/patient/ring');
          bool isConnected = await communicator.getConnectionStateV2();

          if (isOnRingPage) {
            debugPrint(
                "Pull to refresh on devices page. Connected: $isConnected");

            // Check if sync or connection is already in progress
            bool isSyncInProgress = globalController.currentToastStatus.value ==
                    CurrentToastStatus.connecting ||
                globalController.currentToastStatus.value ==
                    CurrentToastStatus.dataSyncing;

            debugPrint(
                "Sync already in progress: $isSyncInProgress, Current status: ${globalController.currentToastStatus.value}");

            if (!isConnected && !isSyncInProgress) {
              // Device is not connected but may have been connected before
              // Try to auto-connect and sync
              String macId = await prefsService.getLastConnectedDeviceMac();
              if (macId.isNotEmpty) {
                debugPrint(
                    "Attempting to auto-connect to previous device: $macId");
                globalController.currentToastStatus.value =
                    CurrentToastStatus.connecting;

                await communicator.connectToDeviceV2(
                  macId: macId,
                  updateRingStatusToWebView: updateRingStatusToWebView,
                  reloadWebView: reloadWebView,
                  updateDeviceConnectionDetailsToWebview:
                      updateDeviceConnectionDetailsToWebview,
                  updateBetteryStatusInWebView: updateBatteryStatusToWebview,
                );
              }
            } else if (isConnected && !isSyncInProgress) {
              // Device is already connected and no sync in progress, fetch all data and sync again
              debugPrint(
                  "Device already connected, fetching all data and re-syncing");
              globalController.currentToastStatus.value =
                  CurrentToastStatus.dataSyncing;

              await communicator.fetchAllData(
                updateRingStatusToWebView: updateRingStatusToWebView,
                reloadWebView: reloadWebView,
                updateBetteryStatusInWebView: updateBatteryStatusToWebview,
              );
            } else {
              // Either sync is already in progress or we're already connecting
              debugPrint(
                  "Sync or connection already in progress, just refreshing UI");
            }
          }

          // Reload webview after the operations
          await inAppWebViewController?.reload();
          await reinitializeAfterRefresh();
        }
      },
    );
  }

  Future<void> reinitializeAfterRefresh() async {
    refreshDebounceTimer?.cancel();
    refreshDebounceTimer = Timer(const Duration(milliseconds: 500), () async {
      _isJavaScriptInjected = false;
      await injectJavaScript();
      injectUrlChangeMonitor();
      setWebViewListeners();

      if (currentUrl.value.contains('ring')) {
        await updateHealthDataByIdInWebView();
      }
      // updateDeviceConnectionDetailsToWebview();
      // updateBatteryStatusToWebview();
      // updateRingStatusToWebView();
    });
  }

  setNavbarVisibilityAndConnect() async {
    bool visibility = await prefsService.getShouldShowNavbar();
    globalController.shouldShowNavbar.value = visibility;
    if (visibility == false) {
      prefsService.setUid("");
    }
    autoConnect();
  }

  Future<void> onWebViewLoadStop(
      InAppWebViewController controller, Uri? url) async {
    if (url != null) {
      currentUrl.value = url.toString();
      debugPrint("WebView loaded: ${url.toString()}");

      // Reset the JavaScript injection flag when navigating to a new page
      _isJavaScriptInjected = false;

      // Wait for the page to be fully loaded
      await Future.delayed(const Duration(milliseconds: 500));

      if (!_isJavaScriptInjected) {
        debugPrint("Injecting JavaScript after page load");
        await injectJavaScript();
        injectUrlChangeMonitor();
        _isJavaScriptInjected = true;

        // If on ring page, update the health data
        if (url.toString().contains('ring')) {
          debugPrint("Ring page detected during page load, fetching data");
          await fetchTodayDataOnly();
        }
      }

      // Mark webview as ready and check for pending NFC dialog
      if (!_isWebViewReady) {
        _isWebViewReady = true;
        debugPrint("🏷️ [NFC] WebView is now ready");
        await getProgramsFromWebView();
        // Check if there's a pending NFC dialog to show
        if (_hasPendingNfcDialog) {
          debugPrint(
              "🏷️ [NFC] Showing pending NFC dialog now that WebView is ready");
          _hasPendingNfcDialog = false;
          // Add a small delay to ensure UI is stable
          Future.delayed(const Duration(milliseconds: 800), () {
            _showNfcDialog();
          });
        }
      }
    }
  }

  void navigateToUrl(String url) {
    if (inAppWebViewController != null) {
      try {
        inAppWebViewController?.loadUrl(
          urlRequest: URLRequest(url: WebUri(url)),
        );
      } catch (e) {
        debugPrint('Error navigating to URL: $e');
        // Controller might have been disposed
      }
    }
  }

  // Data Collection Methods
  Future<void> getAllDataFromFirebase() async {
    try {
      String uid = await prefsService.getUid();

      if (uid.isEmpty) {
        debugPrint("User ID is empty, cannot fetch data from Firebase");
        return;
      }

      // Properly await the Future.wait to ensure all data is loaded
      await Future.wait([
        getSleepDataFromFirebaseV2(),
        getOxygenDataFromFirebaseV2(),
        getStepsDataFromFirebaseV2(),
        getHrvDataFromFirebaseV2(),
        getLastSyncedTimeFromFirebase(),
      ]);
    } catch (e) {
      debugPrint("Error fetching data from Firebase: $e");
    }
  }

  Future<void> getLastSyncedTimeFromFirebase() async {
    lastSyncedMillisecondsSinceEpoch =
        await ringDataCollector.getLastSyncedAtTime();
  }

  // Future<void> getCombinedDataFromFirebase() async {
  //   try {
  //     Map<String, dynamic>? combinedData =
  //         await ringDataCollector.getLatestDocumentFromCollection(
  //             healthDataType: HealthDataType.combinedData);
  //     if (combinedData != null) {
  //       combinedFirebaseModel = CombinedFirebaseModel.fromJson(combinedData);
  //     } else {
  //       debugPrint("No combined data found.");
  //     }
  //   } catch (e) {
  //     debugPrint("Error fetching combined data: $e");
  //   }
  // }

  Future<void> getSleepDataFromFirebaseV2() async {
    try {
      Map<String, dynamic>? sleepData = await ringDataCollector.getLatestDataV2(
          collectionNameV2: V2CollectionNames.saiwellRingSleepV2);
      if (sleepData != null) {
        sleepFirebaseModel = SleepFirebaseModel.fromJson(sleepData);
      }
    } catch (e) {
      debugPrint("Error fetching sleep data: $e");
    }
  }

  Future<void> getOxygenDataFromFirebaseV2() async {
    try {
      Map<String, dynamic>? data = await ringDataCollector.getLatestDataV2(
          collectionNameV2: V2CollectionNames.saiwellRingBloodOxygenV2);
      if (data != null) {
        oxygenFirebaseV2Model = OxygenFirebaseV2Model.fromJson(data);
      }
    } catch (e) {
      debugPrint("Error fetching blood oxygen data: $e");
    }
  }

  Future<void> getStepsDataFromFirebaseV2() async {
    try {
      Map<String, dynamic>? data = await ringDataCollector.getLatestDataV2(
          collectionNameV2: V2CollectionNames.saiwellRingStepV2);

      if (data != null) {
        stepFirebaseV2Model = StepFirebaseV2Model.fromJson(data);
      }
    } catch (e) {
      debugPrint("Error fetching steps data: $e");
    }
  }

  Future<void> getHrvDataFromFirebaseV2() async {
    try {
      Map<String, dynamic>? data = await ringDataCollector.getLatestDataV2(
          collectionNameV2: V2CollectionNames.saiwellRingHrvV2);
      if (data != null) {
        hrvFirebaseV2Model = HrvFirebaseV2Model.fromJson(data);
      }
    } catch (e) {
      debugPrint("Error fetching HRV data: $e");
    }
  }

  //
  // Future<void> getSportsDataFromFirebase() async {
  //   try {
  //     Map<String, dynamic>? sportsData =
  //         await ringDataCollector.getLatestDocumentFromCollection(
  //             healthDataType: HealthDataType.sport);
  //     if (sportsData != null) {
  //       sportFirebaseModel = SportFirebaseModel.fromJson(sportsData);
  //       debugPrint("Sports data retrieved successfully.");
  //     } else {
  //       debugPrint("No sports data found.");
  //     }
  //   } catch (e) {
  //     debugPrint("Error fetching sports data: $e");
  //   }
  // }

  void autoConnect() {
    ringConnectorScheduler.startRingCollection(
      checkAndFetchData: () async {
        isHomePageRequiredInWebView.value = false;
      },
      reloadWebView: () {
        reloadWebView();
      },
      updateBatteryStatusToWebview: () {
        updateBatteryStatusToWebview();
      },
      updateDeviceConnectionDetailsToWebview: () {
        updateDeviceConnectionDetailsToWebview();
      },
    );
  }

  void reloadWebView() {
    Future.delayed(const Duration(seconds: 1), () {
      if (inAppWebViewController != null) {
        try {
          // First clear JavaScript cache
          inAppWebViewController?.evaluateJavascript(
            source: "window.location.reload(true);",
          );
          print("---------TRYING RELOADING WITH CACHE CLEARING");
        } catch (e) {
          debugPrint('Error reloading WebView: $e');
          // Controller might have been disposed
        }
      }
    });
  }

  // Future<void> checkAndFetchData() async {
  //   await ringDataCollector.checkAndFetchData(
  //       updateDeviceConnectionDetailsToWebview:
  //           updateDeviceConnectionDetailsToWebview,
  //       updateBatteryStatusToWebview: updateBatteryStatusToWebview,
  //       updateRingStatusToWebView: () {},
  //       isHealthDataRequired: true);
  // }

  // Future<void> startCollectingData() async {
  //   if (!dataCollectionStarted.value) {
  //     ringDataCollector.checkAndFetchData(
  //       updateDeviceConnectionDetailsToWebview:
  //           updateDeviceConnectionDetailsToWebview,
  //       updateBatteryStatusToWebview: updateBatteryStatusToWebview,
  //       updateRingStatusToWebView: updateRingStatusToWebView,
  //       isHealthDataRequired: false,
  //     );
  //     ringDataCollector.startDataCollection(
  //       updateDeviceConnectionDetailsToWebview:
  //           updateDeviceConnectionDetailsToWebview,
  //       updateBatteryStatusToWebview: updateBatteryStatusToWebview,
  //       updateRingStatusToWebView: updateRingStatusToWebView,
  //     );
  //     dataCollectionStarted.value = true;
  //   }
  // }

  Future<void> _postUidAndGetCustomToken(String uid) async {
    String? token = await BackendApi.postUidAndGetCustomToken(uid: uid);
    if (token != null) {
      await authService.loginWithCustomToken(token);
    }
  }

  // Location Methods
  void _fetchLocation() async {
    debugPrint("called -----------fetchLocation ");
    LocationData location = await determineLocation();
    debugPrint("location --- --  ${location.altitude}");
    _postLocationData(location);
    _subscribeLocation();
  }

  void _subscribeLocation() async {
    Stream<LocationData> stream = subscribeForLocationChange();
    await locationSubscription?.cancel();
    locationSubscription = stream.listenSafely((data) {
      _postLocationData(data);
    });
  }

  void _postLocationData(LocationData data) async {
    String uid = await prefsService.getUid();
    if (uid != "") {
      BackendApi.initiatePostAmbeeApiCall(uid, data.latitude, data.longitude);
    }
  }

  void _fetchHealthData(String uid) async {
    HealthService service = HealthService();
    await service.fetchHealthDataWithDevicesAndSources(uid);
  }

  // WebView Methods
  void setWebViewListeners() {
    _setLoginLogoutHandler();
    _setShowMenuHandler();
    _setReactEventHandler();

    _setConnectToDeviceHandler();
    _setUrlChangeHandler();
    _setRecordAudioHandler();
    _setRegisterButtonClickedHandler();
    _setEmailLoginClickedHandler();
    _setContinueClickedHandler();
    _setGTPlusHandler();
    _downloadReportEventHandler();
  }

  void injectTextFieldMoniter() {
    inAppWebViewController?.evaluateJavascript(
      source: '''
      window.addEventListener('focusin', (event) => {
         setTimeout(() => {
          event.target.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 300);
      });         
''',
    );
  }

  void _setReactEventHandler() {
    inAppWebViewController?.addJavaScriptHandler(
      handlerName: 'reactEventHandler',
      callback: (args) async {
        debugPrint('Message from React: ${args[0]}');
        try {
          Map<String, dynamic> data = jsonDecode(args[0]);
          String uid = data["uid"] ?? "";
          await _postUidAndGetCustomToken(uid);

          // Reset execution flag to ensure FCM token is updated for new user
          _isExecuted = false;

          fetchUid();
          if (uid.isNotEmpty) {
            prefsService.setUid(uid);
            // Force FCM token update for the new user
            _postFcmToken();
          }
          bool isConnected = await communicator.getConnectionStateV2();
          if (args[0].toString().contains("activate") && !isConnected) {
            // Check if we're coming from a PPG-related URL
            final bool isFromPPG =
                currentUrl.value.toLowerCase().contains('ppg');

            debugPrint(
                "_setReactEventHandler - currentUrl: ${currentUrl.value}");
            debugPrint(
                "_setReactEventHandler - isFromPPG determined as: $isFromPPG");

            if (!Platform.isAndroid) {
              Get.toNamed(RingHomeScreen.routeName,
                  arguments: {'isFromPPG': isFromPPG});
            } else {
              Get.toNamed(RingHomeScreen.routeName,
                  arguments: {'isFromPPG': isFromPPG});
            }
          }
        } catch (e) {
          debugPrint('Error parsing JSON: $e');
        }
      },
    );
  }

  void _downloadReportEventHandler() {
    inAppWebViewController?.addJavaScriptHandler(
      handlerName: 'downloadAudiologyReport',
      callback: (args) async {
        debugPrint('Message from downloadAudiologyReport: ${args[0]}');
        try {
          Map<String, dynamic> data = jsonDecode(args[0]);
          String url = data["reportUrl"] ?? "";
          print("got url - $url");

          if (url.isNotEmpty) {
            await _downloadPdfFile(url);
          }
        } catch (e) {
          debugPrint('Error parsing JSON: $e');
        }
      },
    );
  }

  // Helper method to show status dialogs
  void _showStatusDialog({
    required String title,
    required String message,
    bool isError = false,
    VoidCallback? onButtonPressed,
    String buttonText = "OK",
  }) {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: isError ? Colors.red : Colors.green,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isError ? Icons.error_outline : Icons.check,
                  color: Colors.white,
                  size: 36,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                message,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: onButtonPressed ?? () => Get.back(),
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  buttonText,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to show loading dialog
  void _showLoadingDialog(String message) {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 24),
              const Text(
                "Downloading Report",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                message,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  Future<void> _downloadPdfFile(String url) async {
    try {
      debugPrint('Starting PDF download from: $url');

      // Show loading dialog
      _showLoadingDialog("Please wait while we download your report...");

      // Request storage permissions for Android
      if (Platform.isAndroid) {
        final permissionUtil = PermissionHandlerUtil();
        final hasPermission = await permissionUtil.checkStoragePermission();
        if (!hasPermission) {
          // Close loading dialog
          Get.back();
          _showStatusDialog(
              title: "Permission Denied",
              message: "Storage permission is required to download reports",
              isError: true);
          return;
        }
      }

      // Get the directory to save the file
      final Directory directory;
      if (Platform.isIOS) {
        // On iOS, save to the app's documents directory which can be accessed from Files app
        directory = await getApplicationDocumentsDirectory();
      } else {
        // On Android, try to save to Downloads directory
        final List<Directory>? externalDirs =
            await getExternalStorageDirectories();
        if (externalDirs != null && externalDirs.isNotEmpty) {
          directory = externalDirs.first;
        } else {
          directory = await getApplicationDocumentsDirectory();
        }
      }

      // Generate a filename with timestamp
      final fileName =
          'audiology_report_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final filePath = '${directory.path}/$fileName';
      debugPrint('Will save PDF to: $filePath');

      // Create the HTTP client and set timeout
      final client = HttpClient()
        ..connectionTimeout = const Duration(seconds: 30);

      debugPrint('Sending HTTP request...');
      final request = await client.getUrl(Uri.parse(url));
      final response = await request.close();

      debugPrint('HTTP response received with status: ${response.statusCode}');

      // Close the loading dialog
      Get.back();

      // Check if response is successful
      if (response.statusCode == 200) {
        debugPrint('Converting response to bytes...');
        final bytes = await _consolidateHttpClientResponse(response);
        debugPrint('Received ${bytes.length} bytes');

        if (bytes.isNotEmpty) {
          // Save the file
          final file = File(filePath);
          await file.writeAsBytes(bytes);

          // Verify file was saved
          final bool fileExists = await file.exists();
          final int fileSize = fileExists ? await file.length() : 0;
          debugPrint('File exists: $fileExists, File size: $fileSize bytes');

          if (fileExists && fileSize > 0) {
            // Show success dialog
            final String directoryName = Platform.isIOS
                ? 'Files app > On My iPhone > SAiWELL'
                : 'Downloads';

            _showStatusDialog(
                title: "Download Complete",
                message:
                    "Your report '$fileName' has been saved in $directoryName",
                isError: false,
                buttonText: "CLOSE");
          } else {
            _showStatusDialog(
                title: "Download Failed",
                message: "Failed to save file to: $filePath",
                isError: true);
          }
        } else {
          _showStatusDialog(
              title: "Download Failed",
              message: "Downloaded file is empty",
              isError: true);
        }
      } else {
        _showStatusDialog(
            title: "Download Failed",
            message: "Failed to download report: ${response.statusCode}",
            isError: true);
      }

      client.close();
    } catch (e) {
      // Close loading dialog if open
      if (Get.isDialogOpen == true) {
        Get.back();
      }
      debugPrint('Error downloading PDF: $e');
      _showStatusDialog(
          title: "Download Failed",
          message: "Error downloading report: $e",
          isError: true);
    }
  }

  // Helper method to convert HttpClientResponse to Uint8List
  Future<Uint8List> _consolidateHttpClientResponse(
      HttpClientResponse response) async {
    final List<List<int>> chunks = [];
    int totalLength = 0;

    await for (List<int> chunk in response) {
      chunks.add(chunk);
      totalLength += chunk.length;
    }

    if (chunks.length == 1) {
      return Uint8List.fromList(chunks[0]);
    }

    final Uint8List result = Uint8List(totalLength);
    int offset = 0;
    for (List<int> chunk in chunks) {
      result.setRange(offset, offset + chunk.length, chunk);
      offset += chunk.length;
    }

    return result;
  }

  _setRegisterButtonClickedHandler() {
    inAppWebViewController?.addJavaScriptHandler(
      handlerName: 'registerClicked',
      callback: (args) {
        LogEvents.logRegisterMobileEvent();
      },
    );
  }

  _setGTPlusHandler() {
    inAppWebViewController?.addJavaScriptHandler(
      handlerName: 'gtPlus',
      callback: (args) async {
        print("gtPlus handler called : $args");
        final data = jsonDecode(args[0]) as Map<String, dynamic>;
        final item = data['item'] as String? ?? '';

        const itemToCaptureType = {
          'faceMesh': CaptureType.faceMesh,
          'face': CaptureType.face,
          'lips': CaptureType.lips,
          'teethImage': CaptureType.teeth,
          'tongueImage': CaptureType.tongue,
          'palmImage': CaptureType.palm,
          'nails': CaptureType.nails,
        };

        if (item == "temperatureRecorded") {
          Get.toNamed(TempMeasurementScreen.routeName);
        } else if (item == "ppgRecorded") {
          // Mark that we're in PPG section
          wasOnPPG = true;
          debugPrint(
              "Setting wasOnPPG flag to true - ppgRecorded in gtPlus handler");

          // Check if ring is in connecting or syncing state
          bool isSyncInProgress = globalController.currentToastStatus.value ==
                  CurrentToastStatus.connecting ||
              globalController.currentToastStatus.value ==
                  CurrentToastStatus.dataSyncing;

          if (isSyncInProgress) {
            // Show snackbar with error if ring is connecting or syncing
            Get.snackbar(
              'Cannot start PPG recording',
              'Please wait until ring connection and data syncing is complete',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.red,
              colorText: Colors.white,
              duration: const Duration(seconds: 3),
            );
            return;
          }

          // Check if ring is connected before starting PPG
          bool isConnected = await communicator.getConnectionStateV2();
          if (!isConnected) {
            // Redirect to ring connection screen if not connected
            if (!Platform.isAndroid) {
              Get.toNamed(RingHomeScreen.routeName,
                  arguments: {'isFromPPG': true});
            } else {
              Get.toNamed(RingHomeScreen.routeName,
                  arguments: {'isFromPPG': true});
            }
          } else {
            if (ppgController.isRecordedOnProgress.value) {
              Get.toNamed(PpgGraphScreen.routeName);
            } else {
              Get.toNamed(PpgInstructionScreen.routeName);
            }
          }
        } else if (item == "speechRecorded") {
          Get.toNamed(
            RecordingInstructionScreen.routeName,
            arguments: {
              "isFromGtPlus": true,
            },
          );
        } else {
          final captureType = itemToCaptureType[item];
          if (captureType == null) {
            print("Unknown item type: $item");
            return;
          }

          Get.toNamed(
            CameraInstructionScreen.routeName,
            arguments: {
              "type": captureType,
            },
          );
        }
      },
    );
  }

  _setLoginLogoutHandler() {
    inAppWebViewController?.addJavaScriptHandler(
      handlerName: 'patientLogin',
      callback: (args) async {
        print("_setLoginLogoutHandler called with $args");
        try {
          Map<String, dynamic> data = jsonDecode(args[0]);
          bool isUserLoggedIn = data["loggedIn"] ?? false;
          if (isUserLoggedIn) {
            globalController.shouldShowNavbar.value = true;
            prefsService.setShouldShowNavbar(true);
            // Reset execution flag to ensure FCM token gets updated on next fetchUid
            _isExecuted = false;
            // Show notification permission dialog after successful login
            _checkAndShowNotificationPermissionsAfterLogin();
            await Future.delayed(const Duration(seconds: 5));
            await getProgramsFromWebView();
          } else {
            globalController.shouldShowNavbar.value = false;
            prefsService.clearUserData();
            // Clear FCM token from preferences to force update on next login
            prefsService.clearFcmToken();
            authService.logout();
            prefsService.setShouldShowNavbar(false);
          }
        } catch (e) {
          debugPrint('Error parsing JSON: $e');
        }
      },
    );
  }

  _setShowMenuHandler() {
    inAppWebViewController?.addJavaScriptHandler(
      handlerName: 'showMenu',
      callback: (args) {
        print("_setShowMenuHandler called with $args");
        if (args.isNotEmpty) {
          Map<String, dynamic> data = jsonDecode(args[0]);
          shouldShouldExtraMenu.value = data["showBookingsLink"] ?? false;
          if (shouldShouldExtraMenu.value) {
            Navigation extraItem = Navigation(
              label: "Sleep Therapy",
              value: "/sleepTherapy",
              isForNavbar: false,
            );
            if (!globalController.moreNavigationItems.any(
              (item) =>
                  item.label == extraItem.label &&
                  item.value == extraItem.value,
            )) {
              globalController.moreNavigationItems.add(extraItem);
            }
            if (!globalController.moreNavigationItemsImages
                .contains(AppImages.icSleep)) {
              globalController.moreNavigationItemsImages.add(AppImages.icSleep);
            }
          }
        }
      },
    );
  }

  _setEmailLoginClickedHandler() {
    inAppWebViewController?.addJavaScriptHandler(
      handlerName: 'emailLoginClicked',
      callback: (args) {
        LogEvents.logEmailLoginMobileEvent();
      },
    );
  }

  _setContinueClickedHandler() {
    inAppWebViewController?.addJavaScriptHandler(
      handlerName: 'continueClicked',
      callback: (args) {
        LogEvents.logPhoneLoginMobileEvent();
      },
    );
  }

  void _setConnectToDeviceHandler() {
    inAppWebViewController?.addJavaScriptHandler(
      handlerName: 'connectToDevice',
      callback: (args) async {
        debugPrint('Message from connectToDevice: ${args[0]}');
        bool isConnected = await communicator.getConnectionStateV2();
        if (!isConnected) {
          Get.toNamed(RingHomeScreen.routeName);
          if (currentUrl.value.contains('Ring')) {
            LogEvents.logRingConnectionEvent();
          } else if (currentUrl.value.contains('connectedDevices')) {
            LogEvents.logRingConnectionConnectedDevicesEvent();
          }
        } else {
          print("Device is already connected");
        }
      },
    );
  }

  void _setRecordAudioHandler() {
    inAppWebViewController?.addJavaScriptHandler(
      handlerName: 'voiceRecord',
      callback: (args) {
        debugPrint('Message from _setRecordAudioHandler: ${args[0]}');
        Get.toNamed(RecordingInstructionScreen.routeName);
      },
    );
  }

  void _setUrlChangeHandler() {
    inAppWebViewController?.addJavaScriptHandler(
      handlerName: 'urlChangeHandler',
      callback: (args) {
        print("url change start-----${args.toList()}");
        if (args.isNotEmpty) {
          currentUrl.value = args[0].toString();
          debugPrint("URL changed to: ${currentUrl.value}");
          _handleUrlChange(currentUrl.value);
        }
      },
    );
  }

  void startUrlPolling() {
    _urlCheckTimer?.cancel();
    _urlCheckTimer =
        Timer.periodic(const Duration(milliseconds: 500), (timer) async {
      if (inAppWebViewController != null) {
        try {
          final currentUrl = await inAppWebViewController?.getUrl();
          if (currentUrl != null && currentUrl.toString() != _lastKnownUrl) {
            _lastKnownUrl = currentUrl.toString();
            _handleUrlChange(_lastKnownUrl!);
          }
        } catch (e) {
          // The controller may have been disposed or there was an error
          debugPrint('Error in URL polling: $e');
          timer.cancel();
          _urlCheckTimer = null;
        }
      } else {
        // Controller is null, stop polling
        timer.cancel();
        _urlCheckTimer = null;
      }
    });
  }

  Future<void> _handleUrlChange(String url) async {
    debugPrint("URL changed to: $url");
    handleNavBarUrlChangeIndex(url);
    updateDeviceConnectionDetailsToWebview();

    // Track if we were on PPG screen before a redirect
    if (url.toLowerCase().contains('ppg')) {
      wasOnPPG = true;
      debugPrint("Setting wasOnPPG flag to true - current URL contains ppg");
    }

    // Prevent automatic redirection to devices page after ring connection
    if (url.contains('patient/devices')) {
      debugPrint("URL changed to devices page, wasOnPPG = $wasOnPPG");

      if (wasOnPPG) {
        // If we were on PPG and are being redirected to devices, go back to PPG
        debugPrint("Preventing redirect to devices, returning to PPG instead");

        // Clear the flag
        wasOnPPG = false;

        // Navigate back to PPG page
        await inAppWebViewController?.evaluateJavascript(
            source: "window.history.pushState({}, '', '/patient/gtPlus');");
        await Future.delayed(const Duration(milliseconds: 300));
        await inAppWebViewController?.reload();
        return;
      }
    }

    if (url.contains("sendOtp") || url.contains("login")) {
      globalController.shouldShowNavbar.value = false;
      await prefsService.clearUserData();
      await authService.logout();
      communicator.disconnectToDeviceV2();

      // Reset execution flag to ensure FCM token gets updated after login
      _isExecuted = false;
    }

    if (url.contains("ring")) {
      debugPrint("Ring page detected, fetching data");

      // Give the page time to fully load
      await Future.delayed(const Duration(milliseconds: 500));

      // First ensure JavaScript is injected
      await injectJavaScript();

      // Then fetch and update data
      await fetchTodayDataOnly();
    }
  }

  // Direct DOM update as a fallback method
  Future<void> updateDOMElementsDirectly() async {
    if (inAppWebViewController == null) return;

    // Get temperature difference for today
    //final tempData = await ringDataCollector.getTemperatureDifferenceForToday();
    // final tempDifference = tempData['difference'] as num;

    final values = {
      'calories': (stepFirebaseV2Model?.calories ?? 0).toStringAsFixed(0),
      'distance': ((stepFirebaseV2Model?.distance ?? 0)).toStringAsFixed(1),
      'steps': stepFirebaseV2Model?.step ?? 0,
      // 'temperature': tempDifference.toStringAsFixed(2),
      // 'temperature': tempFirebaseModel?.temperature ?? 0, //need to remove
    };

    final script = '''
    (function() {
      const values = ${jsonEncode(values)};
      Object.keys(values).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
          element.textContent = values[key];
        }
      });
    })();
    ''';

    try {
      await inAppWebViewController?.evaluateJavascript(source: script);
    } catch (e) {
      debugPrint("Error in direct DOM update: $e");
    }
  }

  handleNavBarUrlChangeIndex(String url) {
    List<Navigation> mainUrls = globalController.navigationItems;
    List<Navigation> moreUrls = globalController.moreNavigationItems;
    int mainIndex = -1;
    int moreIndex = -1;
    mainIndex = mainUrls.indexWhere((element) {
      bool b = url.contains((element.value == "" || element.value == null)
          ? "no_found"
          : element.value ?? "no_found");
      return b;
    });
    moreIndex = moreUrls.indexWhere((element) {
      bool b = url.contains(element.value ?? "no_found");
      return b;
    });
    if (mainIndex != -1) {
      moreIndex = -1;
      selectedNavBarIndex.value = mainIndex;
    } else if (moreIndex != -1) {
      mainIndex = -1;
      moreIndex = globalController.navigationItems.length + moreIndex;
      selectedNavBarIndex.value = moreIndex;
    } else {
      selectedNavBarIndex.value = -1;
    }
  }

  void injectUrlChangeMonitor() {
    if (inAppWebViewController == null) {
      debugPrint('WebView controller is null');
      return;
    }

    const monitorScript = '''
    if (typeof window.monitorUrlChange === 'undefined') {
      window.monitorUrlChange = function() {
        let lastUrl = window.location.href;
        
        // Method 1: MutationObserver
        const observer = new MutationObserver(function() {
          if (lastUrl !== window.location.href) {
            lastUrl = window.location.href;
            window.flutter_inappwebview.callHandler('urlChangeHandler', lastUrl);
          }
        });
        observer.observe(document, {subtree: true, childList: true});
        
        // Method 2: History API override
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;
        
        history.pushState = function() {
          originalPushState.apply(this, arguments);
          window.flutter_inappwebview.callHandler('urlChangeHandler', window.location.href);
        };
        
        history.replaceState = function() {
          originalReplaceState.apply(this, arguments);
          window.flutter_inappwebview.callHandler('urlChangeHandler', window.location.href);
        };
        
        // Method 3: Navigation events
        window.addEventListener('popstate', function() {
          window.flutter_inappwebview.callHandler('urlChangeHandler', window.location.href);
        });
        
        window.addEventListener('hashchange', function() {
          window.flutter_inappwebview.callHandler('urlChangeHandler', window.location.href);
        });

        // Initial call
        window.flutter_inappwebview.callHandler('urlChangeHandler', lastUrl);
      };
      
      
      window.monitorUrlChange();
      console.log('URL monitor initialized');
    }
  ''';
    try {
      inAppWebViewController
          ?.evaluateJavascript(source: monitorScript)
          .then((_) => debugPrint('URL monitor injected successfully'))
          .catchError((e) => debugPrint('Error injecting URL monitor: $e'));
    } catch (e) {
      debugPrint('Error injecting URL monitor: $e');
    }
  }

  Future<void> addLoginHandlers() async {
    await inAppWebViewController?.evaluateJavascript(source: """
    const submitBtn = document.querySelector('[data-testid="submit"]');
    const emailLoginBtn = document.querySelector('a[href="/patient/login"]');
    const registerBtn = document.querySelector('.blue-btn');

    if (submitBtn) {
      submitBtn.removeEventListener('click', continueClickHandler);
      submitBtn.addEventListener('click', continueClickHandler);
    }

    if (emailLoginBtn) {
      emailLoginBtn.removeEventListener('click', emailLoginClickHandler);
      emailLoginBtn.addEventListener('click', emailLoginClickHandler);
    }

    if (registerBtn) {
      registerBtn.removeEventListener('click', registerClickHandler);
      registerBtn.addEventListener('click', registerClickHandler);
    }

    function continueClickHandler() {
      window.flutter_inappwebview.callHandler('continueClicked');
    }

    function emailLoginClickHandler() {
      window.flutter_inappwebview.callHandler('emailLoginClicked');
    }

    function registerClickHandler() {
      window.flutter_inappwebview.callHandler('registerClicked');
    }
  """);
  }

  // Health Data Update Methods
  Future<void> updateHealthDataByIdInWebView() async {
    debugPrint("==== updateHealthDataByIdInWebView called ====");
    if (shouldFetchDataFromFB) {
      debugPrint("Fetching fresh data from Firebase...");
      await getAllDataFromFirebase();
      shouldFetchDataFromFB = false;
    } else {
      debugPrint("Using cached Firebase data (shouldFetchDataFromFB is false)");
    }

    // Check if any data was loaded
    if (stepFirebaseV2Model == null &&
        oxygenFirebaseV2Model == null &&
        hrvFirebaseV2Model == null &&
        // tempFirebaseModel == null && //need to remove
        sleepFirebaseModel == null) {
      debugPrint("ERROR: No data loaded from Firebase, forcing a refresh");
      // Force a refresh by setting shouldFetchDataFromFB to true and trying again
      shouldFetchDataFromFB = true;
      await getAllDataFromFirebase();
    }

    await Future.delayed(const Duration(milliseconds: 500));
    if (!_isJavaScriptInjected) {
      await injectJavaScript();
      _isJavaScriptInjected = true;
    }
    await updateRingValuesInWebView();
  }

  String? getFormattedTimeStampForWebView(Timestamp? vitalCollectedTimestamp) {
    if (vitalCollectedTimestamp == null) {
      return null;
    }
    try {
      DateTime dateTime = vitalCollectedTimestamp.toDate();
      return DateFormat('hh:mm a').format(dateTime);
    } catch (e) {
      return null;
    }
  }

  String getFormattedLastSyncTime(int? millisecondsSinceEpoch) {
    if (millisecondsSinceEpoch == null) {
      return "";
    }
    try {
      DateTime dateTime =
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch);
      return DateFormat('MMM dd, yyyy hh:mm a').format(dateTime);
    } catch (e) {
      return "";
    }
  }

  Future<void> updateRingValuesInWebView() async {
    if (inAppWebViewController == null) {
      debugPrint('WebView controller is null');
      return;
    }

    // Debug logging only for essential information
    debugPrint("Updating ring values in WebView with available data");

    // First, make sure the JavaScript function is properly injected
    await injectJavaScript();

    // Get temperature difference directly instead of calculating from all readings
    // final tempData = await ringDataCollector.getTemperatureDifferenceForToday();
    // final tempDifference = tempData['difference'] as num;
    // final tempTimestamp = tempData['timestamp'] as Timestamp?;
    // final tempTimeStamp = getFormattedTimeStampForWebView(tempTimestamp) ?? "";

    // String tempTimeStamp = getFormattedTimeStampForWebView(
    //         tempFirebaseModel?.vitalCollectedTimestamp) ??
    //     ""; //need to remove

    String oxygenTimeStamp = getFormattedTimeStampForWebView(
            oxygenFirebaseV2Model?.vitalCollectedTimestamp) ??
        "";
    String hrvTimeStamp = getFormattedTimeStampForWebView(
            hrvFirebaseV2Model?.vitalCollectedTimestamp) ??
        "";

    final values = {
      'calories': (stepFirebaseV2Model?.calories ?? 0).toStringAsFixed(0),
      'distance': ((stepFirebaseV2Model?.distance ?? 0)).toStringAsFixed(1),
      'steps': stepFirebaseV2Model?.step ?? 0,
      'bloodOxygen': oxygenFirebaseV2Model?.bloodOxygen ?? 0,
      'heartRate': hrvFirebaseV2Model?.heartRate ?? 0,
      'hrv': hrvFirebaseV2Model?.hrv ?? 0,
      // 'temperature': tempFirebaseModel?.temperature ?? 0, //need to remove
      //  'temperature': tempDifference.toStringAsFixed(2),
      'heartRateTime': hrvTimeStamp,
      'hrvTime': hrvTimeStamp,
      //     'temperatureTime': tempTimeStamp,
      'bloodOxygenTime': oxygenTimeStamp,
      'sleepDurationHours': (((sleepFirebaseModel?.lightSleepMinutes ?? 0) +
                  (sleepFirebaseModel?.deepSleepMinutes ?? 0) +
                  (sleepFirebaseModel?.remSleepMinutes ?? 0)) /
              60)
          .floor(),
      if (sleepFirebaseModel?.lightSleepCount != null)
        'sleepDurationMins': ((sleepFirebaseModel?.lightSleepMinutes ?? 0) +
                (sleepFirebaseModel?.deepSleepMinutes ?? 0) +
                (sleepFirebaseModel?.remSleepMinutes ?? 0)) %
            60,
      'lightSleepHours':
          ((sleepFirebaseModel?.lightSleepMinutes ?? 0) / 60).floor(),
      if (sleepFirebaseModel?.lightSleepCount != null)
        'lightSleepMins': (sleepFirebaseModel?.lightSleepMinutes ?? 0) % 60,
      'deepSleepHours':
          ((sleepFirebaseModel?.deepSleepMinutes ?? 0) / 60).floor(),
      if (sleepFirebaseModel?.lightSleepCount != null)
        'deepSleepMins': (sleepFirebaseModel?.deepSleepMinutes ?? 0) % 60,
      'remSleepHours':
          ((sleepFirebaseModel?.remSleepMinutes ?? 0) / 60).floor(),
      if (sleepFirebaseModel?.lightSleepCount != null)
        'remSleepMins': (sleepFirebaseModel?.remSleepMinutes ?? 0) % 60,
      'lastSyncedAt': lastSyncedMillisecondsSinceEpoch != null
          ? getFormattedLastSyncTime(lastSyncedMillisecondsSinceEpoch)
          : "",
    };

    try {
      // Use a robust approach to update the values - use direct DOM manipulation if function fails
      final javaScriptCall = '''
      try {
        if (typeof window.updateRingValues === 'function') {
          window.updateRingValues(${jsonEncode(values)});
        } else {
          // Fallback to direct DOM manipulation
          console.log("updateRingValues not found, using fallback");
          const values = ${jsonEncode(values)};
          const keys = Object.keys(values);
          keys.forEach((key) => {
            const element = document.getElementById(key);
            if (element) {
              element.textContent = values[key];
            }
          });
        }
      } catch(e) {
        console.error("Error updating ring values:", e);
      }
      ''';

      await inAppWebViewController?.evaluateJavascript(source: javaScriptCall);
      debugPrint('Values updated successfully');
    } catch (e) {
      debugPrint('Error updating values: $e');
    }
  }

  // JavaScript Injection Methods
  Future<void> injectJavaScript() async {
    const javaScriptFunction = '''
    (function() {
      if (typeof window.updateRingValues === "undefined") {
          window.updateRingValues = function(values) {
              function updateElementById(id, value) {
                  const element = document.getElementById(id);
                  if (element) {
                      element.textContent = value;
                  }
              }

              const keys = [
                  "calories",
                  "distance",
                  "steps",
                  "bloodOxygen",
                  "heartRate",
                  "hrv",         
                  "sleepDurationHours",
                  "sleepDurationMins",
                  "lightSleepHours",
                  "lightSleepMins",
                  "deepSleepHours",
                  "deepSleepMins",
                  "remSleepHours",
                  "remSleepMins",
                  "heartRateTime",
                  "hrvTime",
                  "bloodOxygenTime",
                  "lastSyncedAt"
              ];

              keys.forEach((key) => {
                  if (values[key] !== undefined) {
                      updateElementById(key, values[key]);
                  }
              });
          };
      }
    })();
    ''';

    try {
      await inAppWebViewController?.evaluateJavascript(
          source: javaScriptFunction);
    } catch (e) {
      debugPrint('Error injecting JavaScript: $e');
    }
  }

  // Helper Methods
  Future<void> fetchUid() async {
    _lock.synchronized(() async {
      String uid = await prefsService.getUid();
      debugPrint("fetchUid called - Current UID: '$uid'");

      if (_isExecuted && uid != "") {
        debugPrint("fetchUid already executed for this UID, returning");
        return;
      }

      _isExecuted = true;

      if (uid == "") {
        debugPrint("WARNING: Empty UID in fetchUid, no data will be fetched");
        return;
      }

      debugPrint("Setting UID and fetching data for: $uid");
      await prefsService.setUid(uid);
      _fetchLocation();
      _postFcmToken();
      _fetchHealthData(uid);

      // After setting UID, reset the flag to fetch fresh data
      shouldFetchDataFromFB = true;
    });
  }

  void _postFcmToken() async {
    FirestoreService service = FirestoreService();
    String uid = await prefsService.getUid();
    debugPrint("=== User UID fetched: $uid ===");

    if (fcmToken != null) {
      debugPrint("=== FCM Token is not null: $fcmToken ===");
      String currentFcmToken = await prefsService.getFcmToken();
      debugPrint("=== Current FCM Token from prefs: $currentFcmToken ===");

      if (currentFcmToken != fcmToken) {
        debugPrint("=== FCM Token is different, saving new token ===");
        await service.storeFcmToken(uid, fcmToken!);
        debugPrint("=== FCM Token stored successfully for user: $uid ===");
        await prefsService.setFcmToken(fcmToken!);
        debugPrint("=== FCM Token updated in prefs for user: $uid ===");
      } else {
        debugPrint(
            "=== FcmToken not required to save for user: $uid ===  --fcm:$currentFcmToken");
      }
    } else {
      debugPrint("=== FCM Token is null, no action taken ===");
    }
  }

  // Device Status Update Methods
  void updateDeviceConnectionDetailsToWebview() async {
    if (inAppWebViewController == null) {
      debugPrint("[DeviceConnection] WebView Controller is null");
      return;
    }

    try {
      bool isConnected = await communicator.getConnectionStateV2();
      Map<String, dynamic> dataToSend = {"SaiwellRing": isConnected};
      debugPrint("[DeviceConnection] Sending data to WebView: $dataToSend");
      var result = await inAppWebViewController?.evaluateJavascript(
        source: "window.deviceConnectionDetails(JSON.stringify($dataToSend));",
      );
      debugPrint("[DeviceConnection] JavaScript response: $result");
      debugPrint("[DeviceConnection] Response type: ${result.runtimeType}");
    } catch (e) {
      debugPrint("[DeviceConnection] JavaScript execution error: $e");
    }
  }

  void updateBatteryStatusToWebview() async {
    if (inAppWebViewController == null) {
      debugPrint("[BatteryStatus] WebView Controller is null");
      return;
    }
    try {
      Map<String, dynamic> dataToSend = {
        "SaiwellRing": globalController.ringBatteryPercentage.value
      };
      debugPrint("[BatteryStatus] Sending data to WebView: $dataToSend");
      var result = await inAppWebViewController?.evaluateJavascript(
        source: "window.deviceBatteryDetails(JSON.stringify($dataToSend));",
      );
      debugPrint("[BatteryStatus] JavaScript response: $result");
      debugPrint("[BatteryStatus] Response type: ${result.runtimeType}");
    } catch (e) {
      debugPrint("[BatteryStatus] JavaScript execution error: $e");
    }
  }

  void updateRingStatusToWebView() async {
    String uid = await prefsService.getUid();
    Map dataToSend = {
      "uid": uid,
      "type": "device_details",
      "data": {
        "ringVersion": "V2",
        //     "mcuFirmwareVersion": globalController.mcuFirmwareVersion.value,
        "batteryPower": globalController.ringBatteryPercentage.value,
        //     "deviceModel": globalController.model.value,
        "macAddress": Platform.isAndroid
            ? globalController.ringMacAddress.value
            : globalController.macId.value,
        "deviceVersion": globalController.deviceVersion.value,
      }
    };

    if (inAppWebViewController == null) {
      debugPrint("[RingStatus] WebView Controller is null");
      return;
    }

    try {
      String jsonString = jsonEncode(dataToSend);
      jsonString = jsonString.replaceAll("'", "\\'");
      debugPrint("[RingStatus] Sending data to WebView: $jsonString");
      var result = await inAppWebViewController?.evaluateJavascript(
        source: "window.ringStatusUpdate('$jsonString');",
      );
      debugPrint("[RingStatus] JavaScript response: $result");
      debugPrint("[RingStatus] Response type: ${result.runtimeType}");
    } catch (e) {
      debugPrint("[RingStatus] JavaScript execution error: $e");
    }
  }

  Future<void> sendDeepLinkToWebView(String deepLinkUrl) async {
    // Add a small delay to ensure WebView is initialized
    Future.delayed(const Duration(seconds: 2), () async {
      if (inAppWebViewController == null) {
        debugPrint("[sendDeepLinkToWebView] WebView Controller is null");
        reusableSnackBar(
            message: "[sendDeepLinkToWebView] WebView Controller is null");
        return;
      }

      try {
        Map<String, dynamic> dataToSend = {"deepLink": deepLinkUrl};
        String jsonString = jsonEncode(dataToSend);
        jsonString = jsonString.replaceAll("'", "\\'");

        debugPrint(
            "[sendDeepLinkToWebView] Sending data to WebView: $jsonString");
        var result = await inAppWebViewController?.evaluateJavascript(
          source: "window.callReactFunction(JSON.stringify($jsonString));",
        );
        debugPrint("[sendDeepLinkToWebView] JavaScript response: $result");
      } catch (e) {
        debugPrint("[sendDeepLinkToWebView] JavaScript execution error: $e");
      }
    });
  }

  // A special method to force fetching data specifically for today
  Future<void> fetchTodayDataOnly() async {
    try {
      debugPrint("Fetching today's data");
      String uid = await prefsService.getUid();

      if (uid.isEmpty) {
        debugPrint("User ID is empty, cannot fetch today's data");
        return;
      }

      // Clear existing models
      stepFirebaseV2Model = null;
      oxygenFirebaseV2Model = null;
      //     tempFirebaseModel = null; //need to remove
      hrvFirebaseV2Model = null;
      sleepFirebaseModel = null;

      // Fetch all data types for today
      await Future.wait([
        getSleepDataFromFirebaseV2(),
        //getTemperatureDataFromFirebaseV2(), //need to remove
        getOxygenDataFromFirebaseV2(),
        getStepsDataFromFirebaseV2(),
        getHrvDataFromFirebaseV2(),
        getLastSyncedTimeFromFirebase(),
      ]);

      // Update the WebView with whatever data we found
      await updateRingValuesInWebView();
    } catch (e) {
      debugPrint("Error fetching today's data: $e");
    }
  }

  //need to remove
  // Future<void> getTemperatureDataFromFirebaseV2() async {
  //   try {
  //     Map<String, dynamic>? tempData = await ringDataCollector.getLatestDataV2(
  //         collectionNameV2: V2CollectionNames.saiwellRingTemperatureV2);
  //     if (tempData != null) {
  //       tempFirebaseModel = TempFirebaseModel.fromJson(tempData);
  //       tempFirebaseModel?.temperature =
  //           convertToFahrenheit(tempFirebaseModel?.temperature ?? 0);
  //     }
  //   } catch (e) {
  //     debugPrint("Error fetching temperature data: $e");
  //   }
  // }

  double convertToFahrenheit(num celsius) {
    return double.parse(((celsius * 1.8) + 32).toStringAsFixed(2));
  }

  Future<void> getProgramsFromWebView() async {
    if (inAppWebViewController == null) return;
    try {
      final result =
          await inAppWebViewController!.evaluateJavascript(source: '''
          (function() {
            try {
              var userDetails = localStorage.getItem('userDetails');
              if (!userDetails) return null;
              var parsed = JSON.parse(userDetails);
              return parsed.programs || null;
            } catch (e) {
              return null;
            }
          })();
        ''');
      if (result == null) {
        programsFromWebView = null;
      } else if (result is String) {
        programsFromWebView = List<String>.from(jsonDecode(result));
      } else if (result is List) {
        programsFromWebView = List<String>.from(result);
      } else {
        programsFromWebView = null;
      }
      // Store in shared session service
      final userSession = Get.find<UserSessionService>();
      userSession.programsFromWebView = programsFromWebView;
      debugPrint('Extracted programs: '
          '${programsFromWebView?.toString() ?? 'null'}');
      await NotificationManager.startVoiceRecordingNotifications();
      await NotificationManager.startVialsNotifications();
    } catch (e) {
      debugPrint('Error extracting programs: $e');
      programsFromWebView = null;
    }
  }
}
