import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:googleapis/storage/v1.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:SAiWELL/services/prefs_service.dart';
import 'package:SAiWELL/services/firebase_remote_config_service.dart';
import 'package:SAiWELL/constants/constant.dart';
import 'package:SAiWELL/services/gcs_upload_service.dart';

class PPGController extends GetxController {
  MethodChannel? _channel;
  final PrefsService _prefsService = PrefsService();
  final RxList<dynamic> instructionTexts = [].obs;
  final FirebaseRemoteConfigService _firebaseRemoteConfigService =
      FirebaseRemoteConfigService();

  // Observable variables
  final RxList<int> ppgData = <int>[].obs;
  final RxBool isRecording = false.obs;
  final RxDouble completionPercentage = 0.0.obs;

  final RxBool isRecordedOnProgress = false.obs;

  // Increased for smoother scrolling with more data points
  final int maxDataPoints = 1000;

  final RxBool needsFullRefresh = false.obs;
  final int sampleRate = 1; // Show every data point for more detailed waveform

  // Storage for all data received during recording
  final RxList<int> allPPGData = <int>[].obs;

  // Recording duration and timer
  int recordingDurationSeconds = 60;
  Timer? _recordingTimer;
  final Rx<DateTime> _startTime = DateTime.now().obs;

  // Path to the most recently saved CSV file
  final RxString _lastSavedCSVPath = RxString('');

  // Added variables for submit button functionality
  final RxBool canSubmit = false.obs;
  final RxInt remainingSeconds = 0.obs;

  // Loading state for upload
  final RxBool isUploading = false.obs;

  // Data detection timeout
  Timer? _dataDetectionTimer;
  final RxBool hasReceivedData = false.obs;
  final int dataTimeoutSeconds = 5;

  // Get the path to the last saved CSV file
  String? get lastSavedCSVPath =>
      _lastSavedCSVPath.value.isEmpty ? null : _lastSavedCSVPath.value;

  // Chart scaling values - using demo app approach
  final RxDouble yAxisMinValue = (-5000.0).obs;
  final RxDouble yAxisMaxValue = 5000.0.obs;

  @override
  void onInit() {
    super.onInit();
    if (_channel == null) {
      _channel = MethodChannel(Platform.isIOS
          ? 'com.saiwell.sw/ios_native'
          : 'com.saiwell.sw/android_native');
      _setupMethodCallHandler();
    }
    _fetchRecordingDurationFromRemoteConfig();
    _setInstructionList();
  }

  void _setInstructionList() {
    instructionTexts.value =
        _firebaseRemoteConfigService.getPPGInstructionsFromConfig();
    instructionTexts.refresh();
  }

  void _fetchRecordingDurationFromRemoteConfig() {
    try {
      final FirebaseRemoteConfigService remoteConfigService =
          FirebaseRemoteConfigService();
      int duration = remoteConfigService.getRecordingDurationSeconds();
      if (duration > 0) {
        recordingDurationSeconds = duration;
      }
    } catch (e) {
      print('Error fetching recording duration from remote config: $e');
    }
  }

  void _setupMethodCallHandler() {
    print(
        '[PPG Controller] Setting up method call handler on channel: ${Platform.isIOS ? 'com.saiwell.sw/ios_native' : 'com.saiwell.sw/android_native'}');
    _channel?.setMethodCallHandler((call) async {
      print('[PPG Controller] Method call received: ${call.method}');
      switch (call.method) {
        case 'onPPGDataReceived':
          try {
            if (call.arguments is List) {
              final List<dynamic> rawData = call.arguments as List<dynamic>;
              if (rawData.isEmpty) {
                print('[PPG Controller] Received empty PPG data list');
                return;
              }

              // Mark that we've received data to prevent timeout
              hasReceivedData.value = true;

              print(
                  '[PPG Controller] Received ${rawData.length} PPG data points. First few values: ${rawData.take(5)}');

              if (Platform.isIOS) {
                // iOS-specific processing with sampling and scaling
                final List<int> processedData = _processPPGDataForIOS(rawData);
                allPPGData.addAll(processedData);
                ppgData.addAll(processedData);
                print('[PPG Controller] iOS: Added ${processedData.length} processed data points');
              } else {
                // Android: Original simple processing (exactly like before iOS changes)
                // Store raw data directly (inverted by multiplying by -1)
                for (var data in rawData) {
                  allPPGData.add((data as int) * -1);
                }
                print('[PPG Controller] Total accumulated PPG data points: ${allPPGData.length}');

                // Apply sampling to reduce data density if needed
                final List<int> sampledData = [];
                for (int i = 0; i < rawData.length; i++) {
                  if (i % sampleRate == 0) {
                    // Multiply by -1 to reverse/invert the PPG waveform
                    sampledData.add((rawData[i] as int) * -1);
                  }
                }
                print('[PPG Controller] After sampling (rate: $sampleRate), data points for display: ${sampledData.length}');

                // Add to PPG data for display
                ppgData.addAll(sampledData);
              }

              // Calculate min/max for Y-axis scaling
              updateYAxisRange();

              // Remove oldest data points if we exceed max length
              if (ppgData.length > maxDataPoints) {
                print(
                    '[PPG Controller] Trimming display data (exceeded $maxDataPoints points)');
                ppgData.removeRange(0, ppgData.length - maxDataPoints);
              }

              // Trigger UI refresh
              needsFullRefresh.value = true;
            } else {
              print(
                  '[PPG Controller] ERROR: Received invalid PPG data type: ${call.arguments.runtimeType}');
            }
          } catch (e, stackTrace) {
            print('[PPG Controller] ERROR processing PPG data: $e');
            print('[PPG Controller] Stack trace: $stackTrace');
          }
          break;
        case 'onPPGProgressUpdate':
          try {
            if (call.arguments is double) {
              double progress = call.arguments as double;
              print(
                  '[PPG Controller] Received progress update from native: $progress%');
              // Optional: handle progress updates from native side
            }
          } catch (e) {
            print('[PPG Controller] ERROR processing progress update: $e');
          }
          break;
        case 'onPPGStatusUpdate':
          try {
            print(
                '[PPG Controller] Received PPG status update: ${call.arguments}');
            // Handle status updates if needed
          } catch (e) {
            print('[PPG Controller] ERROR processing status update: $e');
          }
          break;
        default:
          print('[PPG Controller] Unhandled method call: ${call.method}');
      }
    });
  }

  // iOS-specific PPG data processing
  List<int> _processPPGDataForIOS(List<dynamic> rawData) {
    final List<int> processedData = [];

    // iOS PPG data processing
    // iOS sends more frequent data (75 points vs Android's 50) and smaller values (12K-65K)
    // Take every 3rd point to reduce density while preserving waveform shape (75 -> 25 points)
    for (int i = 0; i < rawData.length; i += 3) {
      int value = (rawData[i] as int);
      // Invert the waveform and apply iOS-specific scaling
      // Scale up iOS values significantly to make waveform patterns more visible (multiply by 20)
      int processedValue = (value * -20);
      processedData.add(processedValue);
    }
    print('[PPG Controller] iOS: Sampled ${rawData.length} -> ${processedData.length} PPG values with 3x sampling and 20x scaling');
    print('[PPG Controller] iOS: Sample processed values: ${processedData.take(5)}');
    print('[PPG Controller] iOS: Raw input range: ${rawData.cast<int>().reduce((a, b) => a < b ? a : b)} to ${rawData.cast<int>().reduce((a, b) => a > b ? a : b)}');

    return processedData;
  }

  // Calculate Y-axis range optimized for PPG data visualization
  void updateYAxisRange() {
    if (ppgData.isEmpty) return;

    if (Platform.isIOS) {
      // iOS: Show fewer points (100 instead of 200) for clearer wave patterns
      final int visiblePoints = ppgData.length > 100 ? 100 : ppgData.length;
      final List<int> visibleData = ppgData.sublist(ppgData.length - visiblePoints);

      // Find min and max values from visible data
      int minValue = visibleData.reduce((curr, next) => curr < next ? curr : next);
      int maxValue = visibleData.reduce((curr, next) => curr > next ? curr : next);
      int range = maxValue - minValue;

      // iOS: Use extremely minimal padding for maximum zoom
      double padding = range * 0.04; // Only 4% padding for iOS
      if (padding < 800) {
        padding = 800;
      }

      yAxisMinValue.value = (minValue - padding).toDouble();
      yAxisMaxValue.value = (maxValue + padding).toDouble();

      print('[PPG Controller] iOS: PPG visible data range ($visiblePoints points): $minValue to $maxValue (range: $range)');
      print('[PPG Controller] iOS: Updated Y-axis range: ${yAxisMinValue.value} to ${yAxisMaxValue.value}');
    } else {
      // Android: Original behavior - exactly like before iOS changes
      // Calculate range based on the visible data range (last 200 points to match X-axis display)
      final int visiblePoints = ppgData.length > 200 ? 200 : ppgData.length;
      final List<int> visibleData = ppgData.sublist(ppgData.length - visiblePoints);

      // Find min and max values from visible data
      int minValue = visibleData.reduce((curr, next) => curr < next ? curr : next);
      int maxValue = visibleData.reduce((curr, next) => curr > next ? curr : next);
      int range = maxValue - minValue;

      // For PPG data, we want adequate padding to ensure waves stay within bounds
      // Use 20% padding on each side for better containment
      double padding = range * 0.20;

      // Ensure minimum padding for very stable signals
      if (padding < 3000) {
        padding = 3000;
      }

      yAxisMinValue.value = (minValue - padding).toDouble();
      yAxisMaxValue.value = (maxValue + padding).toDouble();

      print('[PPG Controller] Android: PPG visible data range ($visiblePoints points): $minValue to $maxValue (range: $range)');
      print('[PPG Controller] Android: Updated Y-axis range: ${yAxisMinValue.value} to ${yAxisMaxValue.value}');
    }
  }

  void _startTimer() {
    isRecordedOnProgress.value = true;
    _startTime.value = DateTime.now();
    _recordingTimer?.cancel();

    // Reset submit button state
    canSubmit.value = false;
    remainingSeconds.value = recordingDurationSeconds;

    _recordingTimer =
        Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!isRecording.value) {
        timer.cancel();
        return;
      }

      // Calculate elapsed time as a percentage of the total recording duration
      final elapsedSeconds =
          DateTime.now().difference(_startTime.value).inMilliseconds / 1000;
      final percentage = (elapsedSeconds / recordingDurationSeconds) * 100;

      // Update remaining seconds
      remainingSeconds.value =
          recordingDurationSeconds - elapsedSeconds.floor();
      if (remainingSeconds.value < 0) remainingSeconds.value = 0;

      // Update completion percentage (capped at 100%)
      completionPercentage.value = percentage.clamp(0.0, 100.0);

      // When timer is complete, enable submission but don't auto-stop recording
      if (percentage >= 100) {
        canSubmit.value = true;
      }
    });
  }

  // Reset timer for PPG
  void resetPpgTimer() {
    remainingSeconds.value = recordingDurationSeconds;
    canSubmit.value = false;
    isRecordedOnProgress.value = false;
    completionPercentage.value = 0.0;
  }

  // Start PPG wave recording
  Future<bool> startPPGWave() async {
    try {
      print('[PPG Controller] Starting PPG Wave recording');
      // Clear existing data when starting a new recording
      ppgData.clear();
      allPPGData.clear();
      completionPercentage.value = 0.0;
      needsFullRefresh.value = true;
      isRecording.value = true;
      canSubmit.value = false;
      hasReceivedData.value = false;

      // Start the timer to track progress
      _startTimer();

      // Start the data detection timer
      _startDataDetectionTimer();

      print('[PPG Controller] Calling native method: startPPGWaveV2');
      final result = await _channel?.invokeMethod('startPPGWaveV2');
      print('[PPG Controller] Native startPPGWaveV2 returned: $result');
      return isRecording.value;
    } catch (e, stackTrace) {
      print('[PPG Controller] ERROR starting PPG wave: $e');
      print('[PPG Controller] Stack trace: $stackTrace');
      return false;
    }
  }

  // Start a timer to check if data is being received
  void _startDataDetectionTimer() {
    // Cancel any existing timer
    _dataDetectionTimer?.cancel();

    print(
        '[PPG Controller] Starting data detection timer (${dataTimeoutSeconds}s timeout)');
    // Start a new timer
    _dataDetectionTimer = Timer(Duration(seconds: dataTimeoutSeconds), () {
      if (isRecording.value && !hasReceivedData.value) {
        print('[PPG Controller] No PPG data received within timeout period');
        // No data received within the timeout period
        _showNoDataReceivedDialog();
      } else {
        print(
            '[PPG Controller] Data detection check passed - data was received');
      }
    });
  }

  // Show dialog when no data is received
  void _showNoDataReceivedDialog() {
    // Stop recording since no data is being received
    stopPPGWave();

    // Show retry dialog
    Get.dialog(
      AlertDialog(
        title: const Text('No Data Detected'),
        content: const Text(
          'Make sure you are wearing your ring correctly and it is connected.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back(); // Close dialog
            },
            child: const Text('Cancel', style: TextStyle(color: Colors.grey)),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back(); // Close dialog
              // Retry recording
              startPPGWave();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4DB6AC),
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  // Stop PPG wave recording
  Future<bool> stopPPGWave() async {
    try {
      print('[PPG Controller] Stopping PPG Wave recording');
      isRecording.value = false;
      _recordingTimer?.cancel();
      _dataDetectionTimer?.cancel();

      print('[PPG Controller] Calling native method: stopPPGWaveV2');
      final result = await _channel?.invokeMethod('stopPPGWaveV2');
      print('[PPG Controller] Native stopPPGWaveV2 returned: $result');

      // Save data to CSV file
      await saveDataToCSV();

      return !isRecording.value;
    } catch (e, stackTrace) {
      print('[PPG Controller] ERROR stopping PPG wave: $e');
      print('[PPG Controller] Stack trace: $stackTrace');
      return false;
    }
  }

  // Save PPG data to CSV file
  Future<String?> saveDataToCSV() async {
    try {
      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final filename = 'ppg_data_$timestamp.csv';

      print('Saving ${allPPGData.length} PPG data points to CSV');

      // Create a CSV file with PPG data
      final csvFile = await _saveDataToCSV(allPPGData, filename);

      print('CSV file saved: ${csvFile?.path}');

      // Store the path for sharing
      _lastSavedCSVPath.value = csvFile?.path ?? '';

      return filename;
    } catch (e, stackTrace) {
      print('Error saving CSV file: $e');
      print('Stack trace: $stackTrace');
      return null;
    }
  }

  // Helper method to save data to a CSV file
  Future<File?> _saveDataToCSV(List<int> ppgData, String filename) async {
    try {
      // Get directory for storing the file - use platform-specific approach
      final directory = Platform.isAndroid
          ? await getExternalStorageDirectory()
          : await getApplicationDocumentsDirectory();

      if (directory == null) {
        print('Error: Could not get a valid storage directory');
        return null;
      }

      // Create the CSV file
      final file = File('${directory.path}/$filename');

      // Build CSV content with header and data
      final csv = StringBuffer();
      csv.writeln(
          'PPG Data Recording - ${DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now())}');
      csv.writeln('Index,PPG Value');

      for (int i = 0; i < ppgData.length; i++) {
        csv.writeln('$i,${ppgData[i]}');
      }

      // Write to the file
      await file.writeAsString(csv.toString());

      print('CSV file saved successfully to: ${file.path}');
      return file;
    } catch (e, stackTrace) {
      print('Error saving CSV file: $e');
      print('Stack trace: $stackTrace');
      return null;
    }
  }

  // Share CSV files with other apps
  Future<void> shareCSVFiles() async {
    try {
      // If we have a specific file path, share that file directly
      if (_lastSavedCSVPath.value.isNotEmpty) {
        print('Sharing specific CSV file: ${_lastSavedCSVPath.value}');
        final file = File(_lastSavedCSVPath.value);
        if (await file.exists()) {
          await Share.shareXFiles([XFile(_lastSavedCSVPath.value)],
              text: 'PPG Data CSV File');
          return;
        } else {
          print('File does not exist: ${_lastSavedCSVPath.value}');
        }
      }

      // Fallback to sharing all PPG CSV files in directory
      final directory = Platform.isAndroid
          ? await getExternalStorageDirectory()
          : await getApplicationDocumentsDirectory();

      if (directory == null) {
        print('Error: Could not get a valid storage directory for sharing');
        return;
      }

      final files = directory
          .listSync()
          .where((entity) =>
              entity is File &&
              entity.path.endsWith('.csv') &&
              entity.path.contains('ppg_'))
          .map((e) => e.path)
          .toList();

      print('Found ${files.length} CSV files to share');

      if (files.isNotEmpty) {
        await Share.shareXFiles(files.map((path) => XFile(path)).toList(),
            text: 'PPG Data CSV Files');
      } else {
        print('No CSV files found to share');
      }
    } catch (e) {
      print('Error sharing CSV files: $e');
    }
  }

  // Submit PPG data - Upload to Google Cloud Storage and reset the UI
  Future<bool> submitPPGData() async {
    try {
      isUploading.value = true;

      // First stop the PPG Wave if still recording
      if (isRecording.value) {
        await stopPPGWave();
      }

      // Upload the data to Google Cloud Storage
      bool uploadSuccess = await uploadPPGDataToGCS();

      // Reset the UI state
      resetState();

      // Reset the recorded but not submitted flag
      isRecordedOnProgress.value = false;

      isUploading.value = false;
      return uploadSuccess;
    } catch (e) {
      print('Error submitting PPG data: $e');
      isUploading.value = false;
      return false;
    }
  }

  // Reset everything to initial state
  void resetState() {
    resetPpgTimer();
    ppgData.clear();
    allPPGData.clear();
    needsFullRefresh.value = true;
    isRecording.value = false;
    canSubmit.value = false;
    completionPercentage.value = 0.0;
    hasReceivedData.value = false;
    _dataDetectionTimer?.cancel();
  }

  // Upload PPG data to Google Cloud Storage
  Future<bool> uploadPPGDataToGCS() async {
    try {
      if (allPPGData.isEmpty) {
        print("No PPG data available to upload");
        return false;
      }
      final csvData = _convertPPGToCsv();
      final uid = await _prefsService.getUid();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final success = await GcsUploadService.uploadToGcs(
        bytes: utf8.encode(csvData),
        uid: uid.isEmpty ? 'anonymous' : uid,
        type: 'ppgWave',
        timestamp: timestamp,
        contentType: 'text/csv',
        extension: 'csv',
        credentialsAssetPath: 'assets/gCloud/credentials.json',
      );
      if (success) {
        print('Successfully uploaded PPG data to GCS');
      } else {
        print('Failed to upload PPG data to GCS');
      }
      return success;
    } catch (e) {
      print('Error uploading PPG data to GCS: $e');
      return false;
    }
  }

  // Convert PPG data to CSV string
  String _convertPPGToCsv() {
    final csvBuffer = StringBuffer();
    // Add CSV header
    csvBuffer.writeln('index,value');

    // Add data rows
    for (int i = 0; i < allPPGData.length; i++) {
      csvBuffer.writeln('$i,${allPPGData[i]}');
    }

    return csvBuffer.toString();
  }

  void clearData() {
    ppgData.clear();
    allPPGData.clear();
  }

  void updateCompletionPercentage(double percentage) {
    completionPercentage.value = percentage;
  }

  // Set custom recording duration
  void setRecordingDuration(int durationInSeconds) {
    // Allow setting custom duration - must be called before starting recording
    if (!isRecording.value) {
      recordingDurationSeconds = durationInSeconds;
      remainingSeconds.value = durationInSeconds;
    }
  }

  @override
  void onClose() {
    if (isRecording.value) {
      stopPPGWave();
    }
    _recordingTimer?.cancel();
    _dataDetectionTimer?.cancel();
    super.onClose();
  }

  // Format remaining time as MM:SS
  String formatRemainingTime() {
    int minutes = remainingSeconds.value ~/ 60;
    int secs = remainingSeconds.value % 60;
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }
}
