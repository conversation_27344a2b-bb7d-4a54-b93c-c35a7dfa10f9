import 'package:SAiWELL/utils/const/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:SAiWELL/modules/ppg/controller/ppg_controller.dart';
import 'dart:async';
import 'dart:io';

import '../../../utils/const/app_images.dart';

class PpgGraphScreen extends GetView<PPGController> {
  const PpgGraphScreen({super.key});
  static const routeName = "/ppgGraphScreen";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('PPG Recording'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.deepOrange),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          const SizedBox(
            height: 24,
          ),
          Container(
              height: 150,
              margin: const EdgeInsets.symmetric(horizontal: 16),
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.accentTeal,
                borderRadius: BorderRadius.circular(
                  1,
                ),
              ),
              child: Row(
                children: [
                  const SizedBox(
                    width: 20,
                  ),
                  const Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "PPG Monitoring",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(
                          height: 4,
                        ),
                        Text(
                          "Step-by-step guide to setting up and recording PPG.\nEnsure accuracy for better health insights.",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    width: 24,
                  ),
                  Image.asset(
                    AppImages.imgPPGInstruction,
                    fit: BoxFit.cover,
                    height: 150,
                    width: 140,
                  ),
                ],
              )),

          const Spacer(),
          // Control button
          Container(
            height: 180, // Increased height to prevent waves from going out of bounds
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15), // Increased padding
            decoration: const BoxDecoration(
              color: AppColors.darkTeal,
            ),
            child: Obx(() {
              // Use key to force rebuild when needed
              final graphKey = controller.needsFullRefresh.value
                  ? ValueKey(DateTime.now().millisecondsSinceEpoch)
                  : const ValueKey('ppg-graph');

              if (controller.ppgData.isEmpty) {
                return const Center(
                  child: Text(
                    "Click 'Start Recording' below to capture the pulse waveform.",
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.white),
                  ),
                );
              }

              if (controller.needsFullRefresh.value) {
                controller.needsFullRefresh.value = false;
              }

              return ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: SfCartesianChart(
                  key: graphKey,
                  plotAreaBorderWidth: 0,
                  enableSideBySideSeriesPlacement: false,
                  trackballBehavior: TrackballBehavior(enable: false),
                  zoomPanBehavior: ZoomPanBehavior(enablePanning: false),
                  margin: const EdgeInsets.all(8), // Added margin to prevent overflow
                  borderColor: Colors.transparent,
                  primaryXAxis: NumericAxis(
                    isVisible: false,
                    minimum: () {
                      // Platform-specific visible points for clearer wave patterns
                      final maxPoints = Platform.isIOS ? 100 : 200;
                      return controller.ppgData.length > maxPoints
                          ? (controller.ppgData.length - maxPoints).toDouble()
                          : 0.0;
                    }(),
                    maximum: controller.ppgData.isNotEmpty
                        ? controller.ppgData.length.toDouble()
                        : (Platform.isIOS ? 100.0 : 200.0),
                    interval: Platform.isIOS ? 10.0 : 20.0, // Smaller intervals for iOS
                    autoScrollingDelta: Platform.isIOS ? 100 : 200,
                    autoScrollingMode: AutoScrollingMode.end,
                    majorGridLines: const MajorGridLines(width: 0),
                  ),
                  primaryYAxis: NumericAxis(
                    isVisible: false,
                    labelStyle: const TextStyle(
                      fontSize: 8,
                      color: Color(0xFFAAAAAA),
                    ),
                    axisLine: const AxisLine(width: 0),
                    majorTickLines: const MajorTickLines(width: 0),
                    minimum: controller.yAxisMinValue.value,
                    maximum: controller.yAxisMaxValue.value,
                    interval: (controller.yAxisMaxValue.value -
                            controller.yAxisMinValue.value) /
                        5,
                    majorGridLines: const MajorGridLines(
                      width: 0.5,
                      color: Color(0xFFEEEEEE),
                      dashArray: [5, 5],
                    ),
                  ),
                  enableAxisAnimation: false,
                  series: <FastLineSeries<PPGDataPoint, double>>[
                    FastLineSeries<PPGDataPoint, double>(
                      dataSource: List.generate(
                          controller.ppgData.length,
                          (index) => PPGDataPoint(index.toDouble(),
                              controller.ppgData[index].toDouble())),
                      color: Colors.white,
                      width: 2.0,
                      xValueMapper: (PPGDataPoint data, _) => data.x,
                      yValueMapper: (PPGDataPoint data, _) => data.y,
                      animationDuration: 0,
                      enableTooltip: false,
                      emptyPointSettings:
                          const EmptyPointSettings(mode: EmptyPointMode.drop),
                      markerSettings: const MarkerSettings(isVisible: false),
                    )
                  ],
                ),
              );
            }),
          ),
          const Spacer(),
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Timer countdown display - only show when recording or timer complete but not submitted
                Obx(() {
                  final bool showTimer = controller.isRecording.value ||
                      (controller.canSubmit.value &&
                          !controller.isRecording.value);

                  return Visibility(
                    visible: showTimer,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            'Time remaining: ',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF555555),
                            ),
                          ),
                          Text(
                            controller.formatRemainingTime(),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: controller.canSubmit.value
                                  ? AppColors.primaryOrange
                                  : const Color(0xFF555555),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }),

                Row(
                  children: [
                    // Start/Stop Recording button - only visible when not recording
                    Obx(() => controller.isRecording.value
                        ? const SizedBox() // Hide the button when recording
                        : Expanded(
                            child: ElevatedButton(
                              onPressed: () => controller.startPPGWave(),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.primaryOrange,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                elevation: 2,
                                shadowColor:
                                    AppColors.primaryOrange.withOpacity(.3),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: const Text(
                                'Start Recording',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          )),

                    // Submit button - full width when recording, only enabled after timer completion
                    Obx(() {
                      final bool isRecording = controller.isRecording.value;
                      final bool canSubmit =
                          controller.canSubmit.value && isRecording;

                      return isRecording
                          ? Expanded(
                              child: ElevatedButton(
                                onPressed: canSubmit
                                    ? () async {
                                        // Show loading dialog
                                        Get.dialog(
                                          Center(
                                            child: CircularProgressIndicator(
                                              color: AppColors.primaryOrange,
                                            ),
                                          ),
                                          barrierDismissible: false,
                                        );

                                        // Submit PPG data - this will stop recording and upload to GCS
                                        final bool success =
                                            await controller.submitPPGData();

                                        // Close dialog
                                        Get.back();

                                        if (success) {
                                          // Show success message
                                          Get.snackbar(
                                            'Success',
                                            'PPG data uploaded successfully',
                                            backgroundColor: Colors.green[100],
                                            colorText: Colors.green[800],
                                            snackPosition: SnackPosition.BOTTOM,
                                            margin: const EdgeInsets.all(16),
                                          );

                                          // Return to previous screen after a short delay
                                          Future.delayed(
                                              const Duration(seconds: 1), () {
                                            Get.back();
                                          });
                                        } else {
                                          // Show error message but stay on screen
                                          Get.snackbar(
                                            'Error',
                                            'Failed to upload PPG data',
                                            backgroundColor: Colors.red[100],
                                            colorText: Colors.red[800],
                                            snackPosition: SnackPosition.BOTTOM,
                                            margin: const EdgeInsets.all(16),
                                          );
                                        }
                                      }
                                    : null,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: canSubmit
                                      ? AppColors.primaryOrange
                                      : Colors.grey.shade300,
                                  foregroundColor: Colors.white,
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  elevation: canSubmit ? 2 : 0,
                                  shadowColor: canSubmit
                                      ? AppColors.primaryOrange.withOpacity(0.3)
                                      : Colors.transparent,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  disabledForegroundColor: Colors.grey.shade400,
                                  disabledBackgroundColor: Colors.grey.shade200,
                                ),
                                child: Text(
                                  canSubmit ? 'Submit' : 'Recording...',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            )
                          : const SizedBox(
                              width: 12); // Just a spacer when not recording
                    }),
                  ],
                ),
                const SizedBox(
                  height: 16,
                ),
              ],
            ),
          ),
          SizedBox(
            height: 24,
          ),
        ],
      ),
    );
  }
}

// Data point class for PPG chart
class PPGDataPoint {
  PPGDataPoint(this.x, this.y);
  final double x;
  final double y;
}
