import 'dart:convert';
import 'dart:io';

import 'package:SAiWELL/modules/recording/controller/instruction_controller.dart';
import 'package:SAiWELL/utils/reusableWidgets/reusable_snackbar.dart';
import 'package:SAiWELL/utils/stream_utils.dart';
import 'package:audio_waveforms/audio_waveforms.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:googleapis/gamesmanagement/v1management.dart';
import 'package:googleapis/storage/v1.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:share_plus/share_plus.dart';

import '../../../constants/constant.dart';
import '../../../models/health_datatype.dart';
import '../../../services/audio_service.dart';
import '../../../utils/dialogs/success_recording_dialog.dart';
import '../../../utils/reusableWidgets/reusable_dialog.dart';
import '../../home/<USER>';
import '../../../services/gcs_upload_service.dart';

class PlaybackController extends GetxController {
  final AudioRecordingService audioRecordingService = AudioRecordingService();
  final InstructionController _instructionController =
      Get.find<InstructionController>();

  final RxDouble currentPosition = 0.0.obs;
  RxBool isDataUploading = false.obs;
  bool isPlayed = true;

  String? get originalFilePath => audioRecordingService.currentRecordingPath;

  @override
  void onClose() {
    _stopAndResetPlayback();
    super.onClose();
  }

  Future<void> _setupListeners() async {
    audioRecordingService.playerController.onCurrentDurationChanged
        .listenSafely((duration) {
      currentPosition.value = duration.toDouble();
    });
  }

  Future<void> playRecording() async {
    try {
      if (audioRecordingService.playbackState.value == PlaybackState.stopped ||
          audioRecordingService.playbackState.value == PlaybackState.initial) {
        audioRecordingService.playerController = PlayerController();
        await _setupListeners();
        await audioRecordingService.playerController.preparePlayer(
          path: originalFilePath!,
          shouldExtractWaveform: true,
          noOfSamples: 50,
        );
      }
      await audioRecordingService.startPlayback();
    } catch (e) {
      _handleError('Play recording', e.toString());
    }
  }

  Future<void> pausePlayback() async {
    try {
      await audioRecordingService.pausePlayback();
    } catch (e) {
      _handleError('Pause playback', e.toString());
    }
  }

  Future<void> resumePlayback() async {
    try {
      await audioRecordingService.resumePlayback();
    } catch (e) {
      _handleError('Resume playback', e.toString());
    }
  }

  Future<void> stopPlayback() async {
    try {
      await audioRecordingService.stopPlayback();
    } catch (e) {
      _handleError('Stop playback', e.toString());
    }
  }

  Future<void> resetPlayback() async {
    try {
      await _stopAndResetPlayback();
    } catch (e) {
      _handleError('Reset playback', e.toString());
    }
  }

  Future<void> _stopAndResetPlayback() async {
    await audioRecordingService.resetPlayback();

    currentPosition.value = 0.0;

    if (originalFilePath != null) {
      await audioRecordingService.playerController.preparePlayer(
        path: originalFilePath!,
        shouldExtractWaveform: true,
        noOfSamples: 50,
      );
    }
  }

  Future<void> shareRecording() async {
    final filePath = originalFilePath;
    if (filePath == null) {
      _handleError('Share recording', 'No recording available to share');
      return;
    }

    try {
      final file = File(filePath);
      if (!await file.exists()) {
        _handleError('Share recording', 'Recording file not found');
        return;
      }

      // Use share_plus package to share the file
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'Check out my audio recording',
        subject: 'Audio Recording',
      );
    } catch (e) {
      debugPrint("error in sharing : ${e.toString()}");
    }
  }

  Future<Map<String, String>> uploadFileToStorage({
    required BuildContext context,
  }) async {
    try {
      isDataUploading.value = true;
      final filePath = originalFilePath;
      if (filePath == null || filePath.isEmpty) {
        print('Error: No recording path provided');
        reusableSnackBar(message: "Error: No file selected");
        return {"etag": ""};
      }
      final audioFile = File(filePath);
      if (!await audioFile.exists()) {
        print('Error: File does not exist - $filePath');
        reusableSnackBar(message: "Error: File not found");
        return {"etag": ""};
      }
      final fileSize = await audioFile.length();
      if (fileSize == 0) {
        print('Error: File is empty - $filePath');
        reusableSnackBar(message: "Error: Empty file");
        return {"etag": ""};
      }
      final bytes = await audioFile.readAsBytes();
      String uid = await prefsService.getUid();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final success = await GcsUploadService.uploadToGcs(
        bytes: bytes,
        uid: uid.isEmpty ? 'anonymous' : uid,
        type: 'speech',
        timestamp: timestamp,
        contentType: 'audio/wav',
        extension: 'wav',
        credentialsAssetPath: 'assets/gCloud/credentials.json',
        bucketName: GcsUploadService.getVoiceBucketName(),
      );
      if (success) {
        successfullyUploadedDialog(
          context: context,
          message: "Your recording has been uploaded successfully!",
          onTap: () async {
            ReusableDialog.close();
            Get.until((route) => route.settings.name?.contains('home') == true);
          },
        );
        await prefsService.setLastVoiceRecordingTimestamp(DateTime.now().millisecondsSinceEpoch);
        return {"etag": "success"};
      } else {
        reusableSnackBar(message: "Error: Upload failed");
        return {"etag": ""};
      }
    } catch (e) {
      print("🔥 Unexpected Upload Error: $e");
      reusableSnackBar(message: "Error: Failed to upload file");
      return {"etag": ""};
    } finally {
      isDataUploading.value = false;
    }
  }

  void _handleError(String context, String error) {
    debugPrint('$context error: $error');
    Get.snackbar(
      'Error',
      'An error occurred during $context',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
