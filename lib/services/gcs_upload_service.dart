import 'dart:io';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:googleapis/storage/v1.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:SAiWELL/constants/constant.dart';
import 'package:flutter/foundation.dart'
    show TargetPlatform, defaultTargetPlatform, debugPrint;

class GcsUploadService {
  static Future<String> getPlatformString() async {
    if (Platform.isIOS) return 'ios';
    if (Platform.isAndroid) return 'android';
    return 'unknown';
  }

  static Future<String> getAppVersion() async {
    final info = await PackageInfo.fromPlatform();
    return info.version;
  }

  static String getBucketName() {
    return isProdEnv ? 'saiwell-gtplus' : 'saiwell-gtplus-staging';
  }

  static String getVoiceBucketName() {
    return isProdEnv ? 'speech_data_bucket' : 'hydra_test_sh';
  }

  static Future<String> buildGcsPath({
    required String uid,
    required String type,
    required int timestamp,
    String? extension,
  }) async {
    final ext = extension != null ? '.$extension' : '';
    String fileName;
    if (type == 'speech') {
      fileName = '${uid}_${timestamp}$ext';
    } else {
      final platform = await getPlatformString();
      final version = await getAppVersion();
      fileName = '${timestamp}_${platform}_${version}_$type$ext';
    }
    final folder = uid;
    final fullPath = '$folder/$fileName';
    debugPrint('[GCS] buildGcsPath: folder=$folder, fileName=$fileName, fullPath=$fullPath');
    return fullPath;
  }

  static Future<bool> uploadToGcs({
    required List<int> bytes,
    required String uid,
    required String type,
    required int timestamp,
    required String contentType,
    String? extension,
    required String credentialsAssetPath,
    String? bucketName,
  }) async {
    try {
      final gcsPath = await buildGcsPath(
        uid: uid,
        type: type,
        timestamp: timestamp,
        extension: extension,
      );
      final resolvedBucketName = bucketName ?? getBucketName();
      debugPrint(
          '[GCS] Uploading to bucket: $resolvedBucketName, path: $gcsPath, contentType: $contentType, bytes: ${bytes.length}');
      final credentialsJson = await rootBundle.loadString(credentialsAssetPath);
      final credentials =
          ServiceAccountCredentials.fromJson(jsonDecode(credentialsJson));
      final httpClient = await clientViaServiceAccount(
        credentials,
        [
          StorageApi.devstorageReadWriteScope,
          StorageApi.devstorageFullControlScope
        ],
      );
      final storage = StorageApi(httpClient);
      final media = Media(
        Stream.value(bytes),
        bytes.length,
        contentType: contentType,
      );
      final response = await storage.objects.insert(
        Object(name: gcsPath),
        resolvedBucketName,
        uploadMedia: media,
      );
      debugPrint(
          '[GCS] Upload response: id=${response.id}, mediaLink=${response.mediaLink}');
      return response.id != null;
    } catch (e) {
      debugPrint('[GCS] GCS upload error: $e');
      return false;
    }
  }
}
