import 'package:flutter/material.dart';
import 'package:SAiWELL/services/notifications/core/notification_scheduler_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;

/// Temporary helper class for testing notifications when app is killed
/// This can be removed once you verify notifications are working
class NotificationTestHelper {
  static final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  /// Schedule a test notification in 1 minute to verify background delivery
  static Future<void> scheduleTestNotification() async {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'test_channel',
        'Test Notifications',
        channelDescription: 'Test notifications to verify background delivery',
        importance: Importance.max,
        priority: Priority.max,
        playSound: true,
        enableVibration: true,
        icon: '@mipmap/launcher_icon',
        color: Color(0xff6aaa64),
        colorized: true,
        autoCancel: true,
        ongoing: false,
        showWhen: true,
        category: AndroidNotificationCategory.reminder,
        visibility: NotificationVisibility.public,
        actions: <AndroidNotificationAction>[
          AndroidNotificationAction(
            'open_app',
            'Open SAiWELL',
            showsUserInterface: true,
          ),
        ],
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: 'default',
        badgeNumber: null,
        categoryIdentifier: 'test',
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      final DateTime scheduledTime = DateTime.now().add(const Duration(minutes: 1));
      const String title = 'SAiWELL Test Notification';
      const String body = 'This notification was scheduled 1 minute ago. If you see this after killing the app, background notifications are working!';

      await _flutterLocalNotificationsPlugin.zonedSchedule(
        999, // Test notification ID
        title,
        body,
        tz.TZDateTime.from(scheduledTime, tz.local),
        platformChannelSpecifics,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
      );

      debugPrint('Test notification scheduled for ${scheduledTime.toString()}');
      debugPrint('Kill the app now and wait 1 minute to test background delivery!');
    } catch (e) {
      debugPrint('Error scheduling test notification: $e');
    }
  }

  /// Cancel the test notification
  static Future<void> cancelTestNotification() async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(999);
      debugPrint('Test notification cancelled');
    } catch (e) {
      debugPrint('Error cancelling test notification: $e');
    }
  }


  /// Check current notification permissions and settings
  static Future<void> checkNotificationStatus() async {
    try {
      // Check if notifications are enabled
      final bool notificationsEnabled = await NotificationSchedulerService.areNotificationsEnabled();
      debugPrint('Notifications enabled: $notificationsEnabled');

      // Check if exact alarms can be scheduled
      final bool canScheduleExact = await NotificationSchedulerService.canScheduleExactAlarms();
      debugPrint('Can schedule exact alarms: $canScheduleExact');

      // Get pending notifications
      final List<PendingNotificationRequest> pending = await NotificationSchedulerService.getPendingNotifications();
      debugPrint('Pending notifications: ${pending.length}');

      for (final notification in pending) {
        debugPrint('- ID: ${notification.id}, Title: ${notification.title}, Body: ${notification.body}');
      }
    } catch (e) {
      debugPrint('Error checking notification status: $e');
    }
  }

  /// Test production notification scheduling (for debugging)
  static Future<void> testProductionNotificationScheduling() async {
    try {
      debugPrint('=== Testing Production Notification Scheduling ===');
      // Schedule a test production notification in 2 minutes to verify it works
      await _scheduleTestProductionNotification();
      // Check what was scheduled
      await checkNotificationStatus();

      debugPrint('=== Production Notification Test Complete ===');
    } catch (e) {
      debugPrint('Error testing production notification scheduling: $e');
    }
  }

  /// Schedule a test production notification in 2 minutes using production settings
  static Future<void> _scheduleTestProductionNotification() async {
    try {
      // Use the same notification details as production notifications
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'scheduled_reminders', // Use production channel
        'Scheduled Reminders',
        channelDescription: 'Regular health check reminders',
        importance: Importance.max,
        priority: Priority.max,
        playSound: true,
        enableVibration: true,
        icon: '@mipmap/launcher_icon',
        color: Color(0xff6aaa64),
        colorized: true,
        autoCancel: true,
        ongoing: false,
        showWhen: true,
        category: AndroidNotificationCategory.reminder,
        visibility: NotificationVisibility.public,
        actions: <AndroidNotificationAction>[
          AndroidNotificationAction(
            'open_app',
            'Open SAiWELL',
            showsUserInterface: true,
          ),
        ],
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: 'default',
        badgeNumber: null,
        categoryIdentifier: 'production_test',
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      final DateTime scheduledTime = DateTime.now().add(const Duration(minutes: 2));
      const String title = 'SAiWELL Production Test';
      const String body = 'This is a production notification test scheduled 2 minutes ago. If you see this after killing the app, production notifications are working!';

      await _flutterLocalNotificationsPlugin.zonedSchedule(
        998, // Production test notification ID (different from regular test ID 999)
        title,
        body,
        tz.TZDateTime.from(scheduledTime, tz.local),
        platformChannelSpecifics,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
      );

      debugPrint('Production test notification scheduled for ${scheduledTime.toString()}');
      debugPrint('Kill the app now and wait 2 minutes to test production notification delivery!');
    } catch (e) {
      debugPrint('Error scheduling production test notification: $e');
    }
  }
}
